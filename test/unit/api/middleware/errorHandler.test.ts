import errorHandler from '@api/middlewares/errorHandler'
import * as log from '@core/log'
import { Request, Response, NextFunction } from 'express'
import UnauthorizedError from '@core/errors/unauthorizedError'
import NotFoundError from '@core/errors/notFoundError'
import BadRequestError from '@core/errors/badRequestError'

jest.mock('@core/log', () => ({
  logError: jest.fn(),
  logInfo: jest.fn()
}))

describe('errorHandler', () => {
  let req: Partial<Request>
  let res: Partial<Response>
  let next: NextFunction
  let jsonMock: jest.Mock
  let statusMock: jest.Mock
  let logErrorSpy: jest.SpyInstance
  let logInfoSpy: jest.SpyInstance

  beforeEach(() => {
    jest.clearAllMocks()

    logErrorSpy = jest.spyOn(log, 'logError')
    logInfoSpy = jest.spyOn(log, 'logInfo')

    jsonMock = jest.fn()
    statusMock = jest.fn().mockReturnValue({ json: jsonMock })

    req = {
      params: { id: '123' },
      query: { page: '1' },
      body: { email: '<EMAIL>', password: 'secret123' },
      method: 'POST',
      headers: { authorization: 'Bearer token123' }
    }

    res = {
      status: statusMock
    }

    next = jest.fn()
  })

  describe('Server errors (status >= 500)', () => {
    it('should log error with logError and return 500 status', () => {
      const error = new Error('Internal server error')
      error.status = 500

      errorHandler(error, req as Request, res as Response, next)

      expect(logErrorSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Internal server error',
        expect.objectContaining({
          error: expect.objectContaining({
            stack: expect.any(String),
            status: 500
          }),
          params: { id: '123' },
          query: { page: '1' },
          body: { email: '<EMAIL>', password: 'secret123' },
          method: 'POST',
          headers: { authorization: 'Bearer token123' }
        })
      )

      expect(logInfoSpy).not.toHaveBeenCalled()
      expect(statusMock).toHaveBeenCalledWith(500)
      expect(jsonMock).toHaveBeenCalledWith({
        message: 'Internal server error',
        payload: []
      })
    })

    it('should default to 500 status when no status is provided', () => {
      const error = new Error('Unknown error')

      errorHandler(error, req as Request, res as Response, next)

      expect(logErrorSpy).toHaveBeenCalled()
      expect(statusMock).toHaveBeenCalledWith(500)
    })
  })

  describe('Authentication errors (401)', () => {
    it('should log simplified message for "User not found" without request body', () => {
      const error = new UnauthorizedError('User not found')

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Authentication failed: User not found',
        expect.objectContaining({
          error: expect.objectContaining({
            stack: expect.any(String),
            status: 401,
            name: 'Error',
            payload: {}
          }),
          params: { id: '123' },
          query: { page: '1' },
          body: undefined,
          method: 'POST',
          headers: { authorization: 'Bearer token123' }
        })
      )

      expect(logErrorSpy).not.toHaveBeenCalled()
      expect(statusMock).toHaveBeenCalledWith(401)
      expect(jsonMock).toHaveBeenCalledWith({
        message: 'User not found',
        payload: []
      })
    })

    it('should log simplified message for "Password is invalid" without request body', () => {
      const error = new UnauthorizedError('Password is invalid')

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Authentication failed: Password is invalid',
        expect.objectContaining({
          body: undefined
        })
      )

      expect(logErrorSpy).not.toHaveBeenCalled()
      expect(statusMock).toHaveBeenCalledWith(401)
    })

    it('should log normally for other 401 errors that are not auth failures', () => {
      const error = new UnauthorizedError('Token expired')

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Authentication failed: Token expired',
        expect.objectContaining({
          body: undefined
        })
      )

      expect(logErrorSpy).not.toHaveBeenCalled()
      expect(statusMock).toHaveBeenCalledWith(401)
    })
  })

  describe('Other client errors (4xx)', () => {
    it('should log with full context for 400 errors', () => {
      const error = new BadRequestError('Invalid request body')

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Invalid request body',
        expect.objectContaining({
          error: expect.objectContaining({
            stack: expect.any(String),
            status: 400,
            name: 'Error',
            payload: {}
          }),
          params: { id: '123' },
          query: { page: '1' },
          body: { email: '<EMAIL>', password: 'secret123' },
          method: 'POST',
          headers: { authorization: 'Bearer token123' }
        })
      )

      expect(logErrorSpy).not.toHaveBeenCalled()
      expect(statusMock).toHaveBeenCalledWith(400)
    })

    it('should log with full context for 404 errors', () => {
      const error = new NotFoundError('Resource not found')

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Resource not found',
        expect.objectContaining({
          body: { email: '<EMAIL>', password: 'secret123' } // Full body included
        })
      )

      expect(logErrorSpy).not.toHaveBeenCalled()
      expect(statusMock).toHaveBeenCalledWith(404)
    })
  })

  describe('Error payload handling', () => {
    it('should return empty array when no payload', () => {
      const error: any = new Error('Simple error')
      error.status = 400

      errorHandler(error, req as Request, res as Response, next)

      expect(jsonMock).toHaveBeenCalledWith({
        message: 'Simple error',
        payload: []
      })
    })

    it('should return empty array when payload is empty object', () => {
      const error = new BadRequestError('Error with empty payload', {})

      errorHandler(error, req as Request, res as Response, next)

      expect(jsonMock).toHaveBeenCalledWith({
        message: 'Error with empty payload',
        payload: []
      })
    })

    it('should return payload when it has content', () => {
      const payload = { field: 'email', message: 'Invalid email format' }
      const error = new BadRequestError('Validation error', payload)

      errorHandler(error, req as Request, res as Response, next)

      expect(jsonMock).toHaveBeenCalledWith({
        message: 'Validation error',
        payload
      })
    })
  })

  describe('Edge cases', () => {
    it('should handle error without message', () => {
      const error: any = new Error()
      error.status = 400

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        '',
        expect.any(Object)
      )
      expect(statusMock).toHaveBeenCalledWith(400)
      expect(jsonMock).toHaveBeenCalledWith({
        message: '',
        payload: []
      })
    })

    it('should handle request without body', () => {
      const error = new UnauthorizedError('User not found')
      req.body = undefined

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Authentication failed: User not found',
        expect.objectContaining({
          body: undefined
        })
      )
    })

    it('should handle request without headers', () => {
      const error: any = new Error('Test error')
      error.status = 400
      req.headers = undefined

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Test error',
        expect.objectContaining({
          headers: undefined
        })
      )
    })
  })

  describe('Authentication message matching', () => {
    it('should match "User not found" case-sensitively', () => {
      const error = new UnauthorizedError('user not found')

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Authentication failed: user not found',
        expect.objectContaining({
          body: undefined
        })
      )
    })

    it('should match "Password is invalid" case-sensitively', () => {
      const error = new UnauthorizedError('password is invalid')

      errorHandler(error, req as Request, res as Response, next)

      expect(logInfoSpy).toHaveBeenCalledWith(
        'errorHandler',
        'Authentication failed: password is invalid',
        expect.objectContaining({
          body: undefined
        })
      )
    })
  })
})
