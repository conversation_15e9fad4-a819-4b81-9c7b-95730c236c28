import {
  RentCafeV2VirtualTourSent,
  Space
} from '@modules/communities/types/space'
import * as syncVirtualToursService from '@modules/rentCafeV2/services/syncVirtualTours'
import * as rentCafeV2Gateway from '@modules/rentCafeV2/gateways/index'
import * as log from '@core/log'
import * as coreUtil from '@core/util'
import * as spaceService from '@modules/communities/services/space'
import * as spaceRepo from '@modules/communities/repositories/space'
import { mockNode, mockSpace } from '@test/helpers/community.helper'
import {
  setupMocks,
  spaces,
  spyOnUpdate,
  spyOnGateway
} from '@test/helpers/rentCafeV2.helper'
import { mockDXSetting } from '@test/helpers/dxSetting.helper'
import { VirtualTourType } from '@modules/rentCafeV2/types'
import { mockExternalLink } from '@test/helpers/externalLink.helper'

describe('#syncVirtualTours', () => {
  beforeEach(() => {
    jest.restoreAllMocks()
  })

  it('Should send virtual tours to RentCafeV2', async () => {
    const partialSpace = {
      rentCafeV2VirtualTourSent: undefined,
      isComplete: true
    }
    setupMocks(partialSpace)

    // Mock the findSpacesByQuery call for finding representative spaces for floor plans
    jest
      .spyOn(spaceRepo, 'findSpacesByQuery')
      .mockImplementation((query: any) => {
        // If it's the original query for all spaces, return the spaces
        if (query._id && query._id.$in) {
          return Promise.resolve(spaces)
        }
        // If it's looking for spaces with the same floor plan, return the relevant spaces
        if (query['floorPlan.externalId'] === 'floorplan-2') {
          // Return spaces 1, 2, 3 (all have floorplan-2), with space 2 being the model unit
          return Promise.resolve([spaces[1], spaces[2], spaces[3]])
        }
        if (query['floorPlan.externalId'] === 'floorplan-1') {
          // Return only space 0 (has floorplan-1)
          return Promise.resolve([spaces[0]])
        }
        return Promise.resolve([])
      })

    await syncVirtualToursService.syncVirtualTours()

    expect(spyOnUpdate).toHaveBeenCalledWith(spaces[0]._id, {
      rentCafeV2VirtualTourSent: 'published'
    })
    expect(spyOnUpdate).toHaveBeenCalledWith(spaces[1]._id, {
      rentCafeV2VirtualTourSent: 'published'
    })

    expect(spyOnGateway).toHaveBeenCalledTimes(6)
    const calls = spyOnGateway.mock.calls

    expect(calls[0][0]?.body).toEqual({
      apiToken: 'api-token',
      companyCode: 'company-code',
      propertyCode: 'property-code',
      propertyId: 123123,
      floorplanId: 'floorplan-1',
      unitId: spaces[0].unit,
      mediaType: 9,
      url: `https://listings-dev.peek.us/viewer?token=${spaces[0].token}&embed&pageType=unit`,
      publishToWebsite: true
    })
    expect(calls[1][0]?.body).toEqual({
      apiToken: 'api-token',
      companyCode: 'company-code',
      propertyCode: 'property-code',
      propertyId: 123123,
      floorplanId: 'floorplan-2',
      unitId: spaces[1].unit,
      mediaType: 9,
      url: `https://listings-dev.peek.us/viewer?token=${spaces[1].token}&embed&pageType=unit`,
      publishToWebsite: true
    })
    expect(calls[5][0]?.body).toEqual({
      apiToken: 'api-token',
      companyCode: 'company-code',
      propertyCode: 'property-code',
      propertyId: 123123,
      floorplanId: 'floorplan-2',
      unitId: spaces[2].unit,
      mediaType: 5, // floorplan tour
      url: `https://listings-dev.peek.us/viewer?token=${spaces[2].token}&embed&pageType=layout`, // Should use model unit token (spaces[2] is the model unit)
      publishToWebsite: true
    })
  })

  it('Should send unpublish when virtual tours are unpublished on our side', async () => {
    const partialSpace: Partial<Space> = {
      rentCafeV2VirtualTourSent: RentCafeV2VirtualTourSent.Published,
      isComplete: true,
      deletedAt: new Date(),
      nodes: null
    }
    setupMocks(partialSpace)

    await syncVirtualToursService.syncVirtualTours()

    expect(spyOnUpdate).toHaveBeenCalledWith(spaces[0]._id, {
      rentCafeV2VirtualTourSent: 'unpublished'
    })
    expect(spyOnUpdate).toHaveBeenCalledWith(spaces[1]._id, {
      rentCafeV2VirtualTourSent: 'unpublished'
    })

    expect(spyOnGateway).toHaveBeenCalledTimes(6)
    const calls = spyOnGateway.mock.calls

    expect(calls[0][0]?.body).toEqual({
      apiToken: 'api-token',
      companyCode: 'company-code',
      propertyCode: 'property-code',
      propertyId: 123123,
      floorplanId: 'floorplan-1',
      unitId: spaces[0].unit,
      mediaType: 9,
      url: `https://listings-dev.peek.us/viewer?token=${spaces[0].token}&embed&pageType=unit`,
      publishToWebsite: false
    })
  })

  it('Should send publish when virtual tours are published on our side', async () => {
    const partialSpace: Partial<Space> = {
      rentCafeV2VirtualTourSent: null,
      isComplete: true,
      nodes: [mockNode()]
    }
    setupMocks(partialSpace)

    await syncVirtualToursService.syncVirtualTours()

    expect(spyOnUpdate).toHaveBeenCalledWith(spaces[0]._id, {
      rentCafeV2VirtualTourSent: 'published'
    })
    expect(spyOnUpdate).toHaveBeenCalledWith(spaces[1]._id, {
      rentCafeV2VirtualTourSent: 'published'
    })

    expect(spyOnGateway).toHaveBeenCalledTimes(6)
    const calls = spyOnGateway.mock.calls

    expect(calls[0][0]?.body).toEqual({
      apiToken: 'api-token',
      companyCode: 'company-code',
      propertyCode: 'property-code',
      propertyId: 123123,
      floorplanId: 'floorplan-1',
      unitId: spaces[0].unit,
      mediaType: 9,
      url: `https://listings-dev.peek.us/viewer?token=${spaces[0].token}&embed&pageType=unit`,
      publishToWebsite: true
    })
  })

  it('Should not send spaces already sent', async () => {
    const partialSpace: Partial<Space> = {
      rentCafeV2VirtualTourSent: RentCafeV2VirtualTourSent.Published
    }
    setupMocks(partialSpace)

    await syncVirtualToursService.syncVirtualTours()

    expect(spyOnUpdate).not.toHaveBeenCalled()
    expect(spyOnGateway).not.toHaveBeenCalled()
  })

  it.skip('When dxSetting has url, use it instead of the one from secrets/env var', async () => {
    const partialSpace: Partial<Space> = {
      rentCafeV2VirtualTourSent: null
    }
    setupMocks(partialSpace, true)

    await syncVirtualToursService.syncVirtualTours()

    const calls = spyOnGateway.mock.calls
    const calledUrl = calls[0][0]?.url
    expect(calledUrl).toEqual(
      'rentcafev2-url-in-dxsetting/property/websitemedia'
    )
  })

  it('Should log proper error when rent cafe call fails', async () => {
    const partialSpace: Partial<Space> = {
      rentCafeV2VirtualTourSent: null,
      isComplete: true,
      nodes: [mockNode()]
    }
    setupMocks(partialSpace)

    jest
      .spyOn(rentCafeV2Gateway, 'requestToRentCafe')
      .mockRejectedValueOnce(new Error('Error message from rentCafeV2'))

    const spyOnLogError = jest.spyOn(log, 'logError')

    await syncVirtualToursService.syncVirtualTours()
    expect(spyOnUpdate).toHaveBeenCalledTimes(5)

    expect(spyOnLogError).toHaveBeenCalledWith(
      'rent-cafe-v2-sync-virtual-tours',
      `Error syncing virtual tour for unit ${spaces[0].unit}: Error message from rentCafeV2`
    )
  })

  describe('#sendVirtualTour', () => {
    it('should send unpublish when virtual tour is complete and deleted', async () => {
      const dxSetting = mockDXSetting({
        rentCafeV2: {
          url: 'rentcafev2-url-in-dxsetting',
          apiToken: 'api-token',
          propertyCode: 'property-code',
          propertyId: 123123,
          companyCode: 'company-code',
          timezone: 'America/New_York'
        }
      })
      const externalLink = mockExternalLink({
        externalId: 'external-id-1'
      })
      const virtualTourType = VirtualTourType.Unit

      jest.spyOn(coreUtil, 'sleep').mockResolvedValue(true)

      const spyOnPost = jest
        .spyOn(syncVirtualToursService, 'postToRentCafeV2')
        .mockResolvedValueOnce(true)

      const space = mockSpace({
        rentCafeV2VirtualTourSent: RentCafeV2VirtualTourSent.Published,
        isComplete: true,
        deletedAt: new Date(),
        floorPlan: {
          name: 'floorplan-name-1',
          externalId: 'floorplan-id-1'
        }
      })
      const spyOnUpdate = jest
        .spyOn(spaceService, 'updateSpaceById')
        .mockResolvedValueOnce(space)

      await syncVirtualToursService.sendVirtualTour(
        space,
        dxSetting,
        externalLink,
        virtualTourType
      )

      expect(spyOnPost).toHaveBeenCalledWith(
        {
          apiToken: 'api-token',
          companyCode: 'company-code',
          propertyCode: 'property-code',
          propertyId: 123123,
          floorplanId: 'floorplan-id-1',
          mediaType: 9,
          publishToWebsite: false,
          unitId: 'external-id-1',
          url: `https://listings-dev.peek.us/viewer?token=${space.token}&embed&pageType=unit`
        },
        dxSetting
      )

      expect(spyOnUpdate).toHaveBeenCalledWith(space._id.toString(), {
        rentCafeV2VirtualTourSent: 'unpublished'
      })
    })

    it('should send publish when virtual tour is complete and not deleted', async () => {
      const dxSetting = mockDXSetting({
        rentCafeV2: {
          url: 'rentcafev2-url-in-dxsetting',
          apiToken: 'api-token',
          propertyCode: 'property-code',
          propertyId: 123123,
          companyCode: 'company-code',
          timezone: 'America/New_York'
        }
      })
      const externalLink = mockExternalLink({
        externalId: 'external-id-1'
      })
      const virtualTourType = VirtualTourType.Unit

      jest.spyOn(coreUtil, 'sleep').mockResolvedValue(true)

      const spyOnPost = jest
        .spyOn(syncVirtualToursService, 'postToRentCafeV2')
        .mockResolvedValueOnce(true)

      const space = mockSpace({
        rentCafeV2VirtualTourSent: null,
        deletedAt: null,
        isComplete: true,
        floorPlan: {
          name: 'floorplan-name-1',
          externalId: 'floorplan-id-1'
        }
      })
      const spyOnUpdate = jest
        .spyOn(spaceService, 'updateSpaceById')
        .mockResolvedValueOnce(space)

      await syncVirtualToursService.sendVirtualTour(
        space,
        dxSetting,
        externalLink,
        virtualTourType
      )

      expect(spyOnPost).toHaveBeenCalledWith(
        {
          apiToken: 'api-token',
          companyCode: 'company-code',
          propertyCode: 'property-code',
          propertyId: 123123,
          floorplanId: 'floorplan-id-1',
          mediaType: 9,
          publishToWebsite: true,
          unitId: 'external-id-1',
          url: `https://listings-dev.peek.us/viewer?token=${space.token}&embed&pageType=unit`
        },
        dxSetting
      )

      expect(spyOnUpdate).toHaveBeenCalledWith(space._id.toString(), {
        rentCafeV2VirtualTourSent: 'published'
      })
    })

    it('should use model unit token for floor plan tours', async () => {
      const dxSetting = mockDXSetting({
        rentCafeV2: {
          url: 'rentcafev2-url-in-dxsetting',
          apiToken: 'api-token',
          propertyCode: 'property-code',
          propertyId: 123123,
          companyCode: 'company-code',
          timezone: 'America/New_York'
        }
      })
      const externalLink = mockExternalLink({
        externalId: 'external-id-1'
      })
      const virtualTourType = VirtualTourType.FloorPlan

      jest.spyOn(coreUtil, 'sleep').mockResolvedValue(true)

      const spyOnPost = jest
        .spyOn(syncVirtualToursService, 'postToRentCafeV2')
        .mockResolvedValueOnce(true)

      // Create a regular space
      const regularSpace = mockSpace({
        rentCafeV2VirtualTourSent: null,
        deletedAt: null,
        isComplete: true,
        isModelUnit: false,
        token: 'regular-space-token',
        floorPlan: {
          name: 'floorplan-name-1',
          externalId: 'floorplan-id-1'
        }
      })

      // Create a model unit with the same floor plan
      const modelSpace = mockSpace({
        rentCafeV2VirtualTourSent: null,
        deletedAt: null,
        isComplete: true,
        isModelUnit: true,
        token: 'model-unit-token',
        floorPlan: {
          name: 'floorplan-name-1',
          externalId: 'floorplan-id-1'
        }
      })

      // Mock the repository call to return both spaces, with model unit included
      jest
        .spyOn(spaceRepo, 'findSpacesByQuery')
        .mockResolvedValueOnce([regularSpace, modelSpace])

      const spyOnUpdate = jest
        .spyOn(spaceService, 'updateSpaceById')
        .mockResolvedValueOnce(regularSpace)

      await syncVirtualToursService.sendVirtualTour(
        regularSpace,
        dxSetting,
        externalLink,
        virtualTourType
      )

      // Should use the model unit's token for floor plan tour
      expect(spyOnPost).toHaveBeenCalledWith(
        {
          apiToken: 'api-token',
          companyCode: 'company-code',
          propertyCode: 'property-code',
          propertyId: 123123,
          floorplanId: 'floorplan-id-1',
          mediaType: 5,
          publishToWebsite: true,
          unitId: 'external-id-1',
          url: `https://listings-dev.peek.us/viewer?token=model-unit-token&embed&pageType=layout`
        },
        dxSetting
      )

      expect(spyOnUpdate).toHaveBeenCalledWith(regularSpace._id.toString(), {
        rentCafeV2VirtualTourSent: 'published'
      })
    })
  })
})
