import * as internalApi from '@core/request/peekInternalApiClient'
import {
  getLatchAccessCode,
  getLatchDoors,
  syncLatchDevices
} from '@modules/sgt/services/latch'
import * as latchLambdaClient from '@modules/sgt/services/latchLambdaClient'
import * as dxSettingRepo from '@modules/communities/repositories/dxSetting'
import { AccessDeviceModel } from '@modules/sgt/models/accessDevice'
import { AxiosInstance } from 'axios'
import { LatchDoor } from '@modules/sgt/types/latchTypes'
import { mockDXSetting } from '@test/helpers/dxSetting.helper'
import * as log from '@core/log'
import {
  AccessDeviceManufacturer,
  AccessDeviceType
} from '@modules/sgt/types/accessDevice'

const mockDoors: LatchDoor[] = [
  {
    uuid: '123456',
    name: 'Door 1',
    type: 'C1',
    buildingUuid: '123456',
    accessibilityType: 'public',
    isConnected: true,
    device: {
      type: 'C1',
      serialNumber: '123456',
      battery: {
        percentage: 100,
        lastUpdated: 123456
      }
    },
    sdkDoorUuid: '123456'
  }
]

describe('Latch SGT Integration', () => {
  let logInfoSpy: jest.SpyInstance
  let logDebugSpy: jest.SpyInstance
  let logErrorSpy: jest.SpyInstance

  beforeEach(() => {
    const fakeDate = new Date('2024-06-01T00:00:00Z')
    jest.useFakeTimers().setSystemTime(fakeDate)
    jest.clearAllMocks()

    logInfoSpy = jest.spyOn(log, 'logInfo').mockImplementation()
    logDebugSpy = jest.spyOn(log, 'logDebug').mockImplementation()
    logErrorSpy = jest.spyOn(log, 'logError').mockImplementation()
  })

  afterAll(() => {
    jest.useFakeTimers()
  })

  afterEach(() => {
    logInfoSpy.mockRestore()
    logDebugSpy.mockRestore()
    logErrorSpy.mockRestore()
  })

  describe('getLatchAccessCode', () => {
    it('should return access code successfully', async () => {
      const spyOnClientPost = jest.fn().mockResolvedValue({
        data: {
          accessCode: '123456'
        }
      })
      jest.spyOn(internalApi, 'createPeekInternalClient').mockReturnValue({
        post: spyOnClientPost
      } as unknown as AxiosInstance)

      const accessCode = await getLatchAccessCode(
        '123456',
        '2024-06-01',
        '2024-06-02',
        '<EMAIL>'
      )

      expect(spyOnClientPost).toHaveBeenCalledWith('latch/access', {
        doorUuid: '123456',
        startDate: '2024-06-01',
        endDate: '2024-06-02',
        email: '<EMAIL>'
      })

      expect(accessCode).toEqual('123456')
    })

    it('should handle API errors', async () => {
      const spyOnClientPost = jest
        .fn()
        .mockRejectedValue(new Error('API Error'))
      jest.spyOn(internalApi, 'createPeekInternalClient').mockReturnValue({
        post: spyOnClientPost
      } as unknown as AxiosInstance)

      await expect(
        getLatchAccessCode(
          '123456',
          '2024-06-01',
          '2024-06-02',
          '<EMAIL>'
        )
      ).rejects.toThrow('API Error')
    })
  })

  describe('getLatchDoors', () => {
    it('should return doors successfully', async () => {
      const spyOnInvokeLatchListDoorsLambda = jest
        .spyOn(latchLambdaClient, 'invokeLatchListDoorsLambda')
        .mockResolvedValue(mockDoors)

      const doors = await getLatchDoors('123456')

      expect(spyOnInvokeLatchListDoorsLambda).toHaveBeenCalledWith('123456')
      expect(doors).toEqual([
        {
          uuid: '123456',
          name: 'Door 1',
          type: 'C1',
          buildingUuid: '123456',
          accessibilityType: 'public',
          isConnected: true,
          device: {
            type: 'C1',
            serialNumber: '123456',
            battery: { percentage: 100, lastUpdated: 123456 }
          },
          sdkDoorUuid: '123456'
        }
      ])
    })
  })

  describe('syncLatchDevices', () => {
    it('should sync latch devices successfully', async () => {
      jest
        .spyOn(latchLambdaClient, 'invokeLatchListDoorsLambda')
        .mockResolvedValue(mockDoors)

      const dxSetting = mockDXSetting({
        latch: {
          buildingUuid: '123456'
        }
      })
      const spyOnFindDXSettingById = jest
        .spyOn(dxSettingRepo, 'findDXSettingById')
        .mockResolvedValue(dxSetting)

      jest.spyOn(AccessDeviceModel, 'findOne').mockResolvedValue(null)
      const spyOnCreate = jest
        .spyOn(AccessDeviceModel, 'create')
        .mockResolvedValue(null)

      await syncLatchDevices('123456')

      expect(spyOnFindDXSettingById).toHaveBeenCalledWith('123456')
      expect(spyOnCreate).toHaveBeenCalledWith({
        identifier: '123456',
        name: 'Door 1',
        type: AccessDeviceType.Pinpad,
        manufacturer: AccessDeviceManufacturer.Latch,
        community: {
          _id: dxSetting.communityId
        },
        isSeamManaged: false,
        model: 'Generic'
      })

      expect(logInfoSpy).toHaveBeenCalledWith(
        'latch-service',
        'Created Latch device 123456: Door 1'
      )
      expect(logInfoSpy).toHaveBeenCalledWith(
        'latch-service',
        'Successfully synced 1 Latch devices'
      )
      expect(logInfoSpy).toHaveBeenCalledTimes(2)
    })

    it('should update existing latch device successfully', async () => {
      jest
        .spyOn(latchLambdaClient, 'invokeLatchListDoorsLambda')
        .mockResolvedValue(mockDoors)

      const dxSetting = mockDXSetting({
        latch: {
          buildingUuid: '123456'
        }
      })
      const spyOnFindDXSettingById = jest
        .spyOn(dxSettingRepo, 'findDXSettingById')
        .mockResolvedValue(dxSetting)

      const existingDevice = {
        _id: 'existing-device-id',
        identifier: '123456',
        name: 'Old Door Name',
        type: AccessDeviceType.Pinpad,
        manufacturer: AccessDeviceManufacturer.Latch
      }
      jest.spyOn(AccessDeviceModel, 'findOne').mockResolvedValue(existingDevice)
      const spyOnFindByIdAndUpdate = jest
        .spyOn(AccessDeviceModel, 'findByIdAndUpdate')
        .mockResolvedValue(null)

      await syncLatchDevices('123456')

      expect(spyOnFindDXSettingById).toHaveBeenCalledWith('123456')
      expect(spyOnFindByIdAndUpdate).toHaveBeenCalledWith(
        'existing-device-id',
        {
          name: 'Door 1'
        }
      )

      expect(logInfoSpy).toHaveBeenCalledWith(
        'latch-service',
        'Updated Latch device 123456: Door 1'
      )
      expect(logInfoSpy).toHaveBeenCalledWith(
        'latch-service',
        'Successfully synced 1 Latch devices'
      )
      expect(logInfoSpy).toHaveBeenCalledTimes(2)
    })

    it('should handle DX setting not found error', async () => {
      const spyOnFindDXSettingById = jest
        .spyOn(dxSettingRepo, 'findDXSettingById')
        .mockResolvedValue(null)

      await expect(syncLatchDevices('123456')).rejects.toThrow(
        'Error syncing Latch devices: DX Setting with id 123456 not found'
      )

      expect(spyOnFindDXSettingById).toHaveBeenCalledWith('123456')

      expect(logInfoSpy).not.toHaveBeenCalled()
      expect(logDebugSpy).not.toHaveBeenCalled()
      expect(logErrorSpy).not.toHaveBeenCalled()
    })

    it('should handle missing latch buildingUuid error', async () => {
      const dxSetting = mockDXSetting({
        latch: { buildingUuid: '' }
      })
      const spyOnFindDXSettingById = jest
        .spyOn(dxSettingRepo, 'findDXSettingById')
        .mockResolvedValue(dxSetting)

      await expect(syncLatchDevices('123456')).rejects.toThrow(
        'Error syncing Latch devices: Latch buildingUuid not found in DX setting'
      )

      expect(spyOnFindDXSettingById).toHaveBeenCalledWith('123456')

      expect(logInfoSpy).not.toHaveBeenCalled()
      expect(logDebugSpy).not.toHaveBeenCalled()
      expect(logErrorSpy).not.toHaveBeenCalled()
    })
  })
})
