import { ObjectId } from '@modules/communities/types/id'
import { Organization } from '@modules/users/types/organization'

export const OrganizationA: Organization = {
  _id: new ObjectId(),
  name: 'OrganizationA',
  domains: ['organizationa.com'],
  isActive: true,
  apiKeys: [
    {
      _id: new ObjectId().toString(),
      name: 'API Key 1',
      isActive: true,
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
      hash: 'hash',
      createdAt: new Date()
    }
  ],
  deletedAt: null
}

export const OrganizationB: Organization = {
  _id: new ObjectId(),
  name: 'OrganizationB',
  domains: ['organizationb.com'],
  isActive: true,
  apiKeys: [
    {
      _id: new ObjectId().toString(),
      name: 'API Key 1',
      isActive: true,
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
      hash: 'hash',
      createdAt: new Date()
    }
  ],
  deletedAt: null
}
