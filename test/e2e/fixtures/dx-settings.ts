import { Community } from '@modules/communities/types/community'
import {
  DXSetting,
  DXSettingServices
} from '@modules/communities/types/dxSetting'
import { ObjectId } from '@modules/communities/types/id'
import {
  communityA1,
  communityA2,
  communityA3,
  communityB1,
  communityB2,
  communityB3
} from './communities'

export const dxA1 = _createDxSettings(communityA1)

export const dxA2 = _createDxSettings(communityA2, {
  funnel: {
    customerApiKey: '123',
    partnerApiKey: '123'
  }
})

export const dxA3 = _createDxSettings(communityA3, {
  entrata: {
    pass: '123',
    propertyId: '123',
    leadSourceId: '123',
    user: '123',
    subdomain: '123'
  }
})

export const dxA4 = _createDxSettings(communityA3, {
  rentCafe: {
    apiToken: '123',
    url: '123',
    propertyId: '123'
  }
})
export const dxA5 = _createDxSettings(communityA3, {
  rentCafeV2: {
    apiToken: '123',
    timezone: '123',
    companyCode: '123',
    propertyId: '123',
    propertyCode: '123'
  }
})
export const dxA6 = _createDxSettings(communityA3, {
  gsDynamics: {
    apiKey: '123',
    url: '123',
    leadSource: '123',
    timezone: '123'
  }
})
export const dxA7 = _createDxSettings(communityA3, {
  engrain: {
    apiKey: '123',
    iconType: '123',
    name: '123',
    service: '123',
    label: '123',
    assetId: '123',
    sightMapUrl: '123',
    unitMapUrl: '123'
  }
})
export const dxA8 = _createDxSettings(communityA3, {
  smartRent: {
    apiKey: '123',
    apiSecret: '123',
    propertyId: '123'
  }
})
export const dxA9 = _createDxSettings(communityA3, {
  anyoneHome: {
    apiKey: '123',
    listingContactEmail: '123',
    propertyId: '123'
  }
})

export const dxB1 = _createDxSettings(communityB1, {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  realPage: {
    pass: '123',
    siteId: '123',
    user: '123'
  }
})
export const dxB2 = _createDxSettings(communityB2, {
  anyoneHome: {
    apiKey: '123',
    listingContactEmail: '123',
    propertyId: '123'
  }
})
export const dxB3 = _createDxSettings(communityB3, {
  yardi: {
    pass: '123',
    license: '123',
    user: '123',
    url: '123',
    entity: '123',
    propertyId: '123',
    serverName: '123',
    database: '123'
  }
})
export const dxB4 = _createDxSettings(communityB3, {
  rentCafe: {
    apiToken: '123',
    url: '123',
    propertyId: '123'
  }
})
export const dxB5 = _createDxSettings(communityB3, {
  rentCafeV2: {
    apiToken: '123',
    timezone: '123',
    companyCode: '123',
    propertyId: '123',
    propertyCode: '123'
  }
})
export const dxB6 = _createDxSettings(communityB3, {
  gsDynamics: {
    apiKey: '123',
    url: '123',
    leadSource: '123',
    timezone: '123'
  }
})
export const dxB7 = _createDxSettings(communityB3, {
  engrain: {
    apiKey: '123',
    iconType: '123',
    name: '123',
    service: '123',
    label: '123',
    assetId: '123',
    sightMapUrl: '123',
    unitMapUrl: '123'
  }
})
export const dxB8 = _createDxSettings(communityB3, {
  smartRent: {
    apiKey: '123',
    apiSecret: '123',
    propertyId: '123'
  }
})

export const orgADxSettings = [
  dxA1,
  dxA2,
  dxA3,
  dxA4,
  dxA5,
  dxA6,
  dxA7,
  dxA8,
  dxA9
]
export const orgBDxSettings = [dxB1, dxB2, dxB3, dxB4, dxB5, dxB6, dxB7, dxB8]
export const allDxSettings = [...orgADxSettings, ...orgBDxSettings]

function _createDxSettings(
  community: Community,
  service?: Partial<Record<DXSettingServices, any>>
): DXSetting {
  const defaultService = {
    yardi: {
      database: '123',
      pass: '123',
      url: '123',
      propertyId: '123',
      serverName: '123',
      user: '123'
    }
  }
  return {
    _id: new ObjectId().toString(),
    communityId: community._id.toString(),
    service: Object.keys(service ?? defaultService)[0] as DXSettingServices,
    dataExchange: {
      syncPms: true
    },
    ...(service ?? defaultService)
  }
}
