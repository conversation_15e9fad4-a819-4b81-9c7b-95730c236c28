import { ObjectId } from '@modules/communities/types/id'
import { Action } from '@modules/users/types/action'
import { Organization } from '@modules/users/types/organization'
import { Policy } from '@modules/users/types/policy'
import { Role } from '@modules/users/types/role'
import { User } from '@modules/users/types/user'
import { genSaltSync, hashSync } from 'bcryptjs'
import { OrganizationA, OrganizationB } from './organizations'
import { Community } from '@modules/communities/types/community'
import {
  communityA1,
  communityA2,
  communityA3,
  communityA4Inactive,
  communityB1,
  communityB2,
  communityB3,
  communityB4Inactive
} from './communities'

// Actions
export const actionFull = _createAction('full')
export const actionRead = _createAction('read')

// Roles
export const superAdminRole = _createUserRole('admin')
export const orgAdminRole = _createUserRole('organizationAdmin')
export const agentRole = _createUserRole('agent')
export const scannerRole = _createUserRole('scanner')
export const basicRole = _createUserRole('basic')

// Users
export const UserSuperAdminA = _createUser('UserSuperAdmin', [], superAdminRole)
export const UserOrgAdminA1 = _createUser(
  'UserOrgAdminA1',
  [OrganizationA],
  orgAdminRole,
  [communityA1]
)
export const UserAgentA2 = _createUser(
  'UserAgentA2',
  [OrganizationA],
  agentRole,
  [communityA1]
)
export const UserScannerA3 = _createUser(
  'UserScannerA3',
  [OrganizationA],
  scannerRole,
  [communityA1]
)

export const UserOrgAdminB1 = _createUser(
  'UserOrgAdminB1',
  [OrganizationB],
  orgAdminRole
)
export const UserAgentB2 = _createUser(
  'UserAgentB2',
  [OrganizationB],
  agentRole
)
export const UserScannerB3 = _createUser(
  'UserScannerB3',
  [OrganizationB],
  scannerRole
)

export const allUsers = [
  UserSuperAdminA,
  UserOrgAdminA1,
  UserAgentA2,
  UserScannerA3,
  UserOrgAdminB1,
  UserAgentB2,
  UserScannerB3
]

export const allNonSuperAdminUsers = [
  UserOrgAdminA1,
  UserAgentA2,
  UserScannerA3,
  UserOrgAdminB1,
  UserAgentB2,
  UserScannerB3
]

export const allNonOrgAdminUsers = [
  UserAgentA2,
  UserScannerA3,
  UserAgentB2,
  UserScannerB3
]

export const allUsersInOrgA = [UserAgentA2, UserOrgAdminA1, UserScannerA3]
export const allUsersInOrgB = [UserAgentB2, UserOrgAdminB1, UserScannerB3]

export const allOrgAdminUsers = [UserOrgAdminA1, UserOrgAdminB1]

export const allUsersInCommunityA1 = [
  UserAgentA2,
  UserScannerA3,
  UserOrgAdminA1
]

// Policies
export const policies = [
  _createPolicy([actionFull], null, superAdminRole), // Parrots user

  // OrgA
  _createPolicy([actionFull], UserOrgAdminA1, null, null, OrganizationA), // Org admin
  _createPolicy([actionFull], UserOrgAdminA1, null, communityA1, null), // Org admin
  _createPolicy([actionFull], UserOrgAdminA1, null, communityA2, null), // Org admin
  _createPolicy([actionFull], UserOrgAdminA1, null, communityA3, null), // Org admin
  _createPolicy([actionFull], UserOrgAdminA1, null, communityA4Inactive, null), // Org admin

  _createPolicy([actionRead], UserAgentA2, null, communityA1), // Agent
  _createPolicy([actionRead], UserScannerA3, null, communityA1), // Scanner

  // Org B
  _createPolicy([actionFull], UserOrgAdminB1, null, null, OrganizationB), // Org admin
  _createPolicy([actionFull], UserOrgAdminB1, null, communityB1, null), // Org admin
  _createPolicy([actionFull], UserOrgAdminB1, null, communityB2, null), // Org admin
  _createPolicy([actionFull], UserOrgAdminB1, null, communityB3, null), // Org admin
  _createPolicy([actionFull], UserOrgAdminB1, null, communityB4Inactive, null), // Org admin

  _createPolicy([actionRead], UserAgentB2, null, communityB1), // Agent
  _createPolicy([actionRead], UserScannerB3, null, communityB1) // Scanner
]

function _createUser(
  name: string,
  organizations: Organization[] | undefined,
  role: Role,
  communities: Community[] = []
): User {
  return {
    _id: new ObjectId(),
    name,
    organizations,
    deletedAt: null,
    email: `${name}@example.com`,
    roleId: role._id.toString(),
    status: 'active',
    password: hashSync(name, genSaltSync(10)),
    googleCredentials: {
      accessToken: 'accessToken',
      refreshToken: 'refreshToken',
      scope: 'scope',
      tokenType: 'tokenType',
      accountName: 'accountName',
      accountType: 'accountType',
      expiryDate: new Date().getTime()
    },
    communities
  }
}

function _createUserRole(name: string): Role {
  return {
    _id: new ObjectId() as any,
    alias: name,
    name,
    description: name
  }
}

function _createPolicy(
  actions: Action[],
  user: User | null = null,
  role: Role | null = null,
  community: Community | null = null,
  organization: Organization | null = null
): Policy {
  /* eslint-disable */
  const resource = role
    ? 'roles'
    : organization
    ? 'organizations'
    : 'communities'
  /* eslint-enable */
  return {
    _id: new ObjectId().toString(),
    allow: actions.map((action) => ({
      _id: action._id,
      alias: action.alias
    })),
    deny: [],
    requestAllow: [],
    resource,
    communityId: community?._id.toString() ?? null,
    organizationId: organization?._id.toString() ?? null,
    roleId: role?._id.toString() ?? null,
    userId: user?._id.toString() ?? null
  }
}

function _createAction(name: string): Action {
  return {
    _id: new ObjectId() as any,
    alias: name,
    description: name,
    name
  }
}
