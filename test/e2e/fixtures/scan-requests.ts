import { Community } from '@modules/communities/types/community'
import { ObjectId } from '@modules/communities/types/id'

import { <PERSON>annerWorkLog } from '@modules/communities/types/scannerWorkLog'
import {
  ScanRequest,
  ScanRequestStatus
} from '@modules/communities/types/scanRequest'
import { Space } from '@modules/communities/types/space'
import { User } from '@modules/users/types/user'
import {
  communityA1,
  communityA2,
  communityA3,
  communityB1,
  communityB2,
  communityB3
} from './communities'
import {
  SpaceA11,
  SpaceA12,
  SpaceA13,
  SpaceA14,
  SpaceA21,
  SpaceA22,
  SpaceA23,
  SpaceA24,
  SpaceA31,
  SpaceA32,
  SpaceA33,
  SpaceA34,
  SpaceB11,
  SpaceB12,
  SpaceB13,
  SpaceB14,
  SpaceB21,
  SpaceB22,
  SpaceB23,
  SpaceB24,
  SpaceB31,
  SpaceB32,
  SpaceB33,
  SpaceB34
} from './spaces'
import { UserScannerA3, UserScannerB3 } from './users'

export const ScanRequestA11 = _createScanRequest(SpaceA11)
export const ScanRequestA12 = _createScanRequest(SpaceA12)
export const ScanRequestA13 = _createScanRequest(SpaceA13)
export const ScanRequestA14 = _createScanRequest(SpaceA14)
export const ScanRequestA21 = _createScanRequest(SpaceA21)
export const ScanRequestA22 = _createScanRequest(SpaceA22)
export const ScanRequestA23 = _createScanRequest(SpaceA23)
export const ScanRequestA24 = _createScanRequest(SpaceA24)
export const ScanRequestA31 = _createScanRequest(SpaceA31)
export const ScanRequestA32 = _createScanRequest(SpaceA32)
export const ScanRequestA33 = _createScanRequest(SpaceA33)
export const ScanRequestA34 = _createScanRequest(SpaceA34)
export const ScanRequestB11 = _createScanRequest(SpaceB11)
export const ScanRequestB12 = _createScanRequest(SpaceB12)
export const ScanRequestB13 = _createScanRequest(SpaceB13)
export const ScanRequestB14 = _createScanRequest(SpaceB14)
export const ScanRequestB21 = _createScanRequest(SpaceB21)
export const ScanRequestB22 = _createScanRequest(SpaceB22)
export const ScanRequestB23 = _createScanRequest(SpaceB23)
export const ScanRequestB24 = _createScanRequest(SpaceB24)
export const ScanRequestB31 = _createScanRequest(SpaceB31)
export const ScanRequestB32 = _createScanRequest(SpaceB32)
export const ScanRequestB33 = _createScanRequest(SpaceB33)
export const ScanRequestB34 = _createScanRequest(SpaceB34)

export const allScanRequests = [
  ScanRequestA11,
  ScanRequestA12,
  ScanRequestA13,
  ScanRequestA14,
  ScanRequestA21,
  ScanRequestA22,
  ScanRequestA23,
  ScanRequestA24,
  ScanRequestA31,
  ScanRequestA32,
  ScanRequestA33,
  ScanRequestA34,
  ScanRequestB11,
  ScanRequestB12,
  ScanRequestB13,
  ScanRequestB14,
  ScanRequestB21,
  ScanRequestB22,
  ScanRequestB23,
  ScanRequestB24,
  ScanRequestB31,
  ScanRequestB32,
  ScanRequestB33,
  ScanRequestB34
]

export const WorkLogA31 = _createWorkLog(UserScannerA3, communityA1, [
  ScanRequestA31
])
export const WorkLogA32 = _createWorkLog(UserScannerA3, communityA2, [
  ScanRequestA32
])
export const WorkLogA33 = _createWorkLog(UserScannerA3, communityA3, [
  ScanRequestA33
])

export const WorkLogB31 = _createWorkLog(UserScannerB3, communityB1, [
  ScanRequestB31
])
export const WorkLogB32 = _createWorkLog(UserScannerB3, communityB2, [
  ScanRequestB32
])
export const WorkLogB33 = _createWorkLog(UserScannerB3, communityB3, [
  ScanRequestB33
])

export const allWorkLogs = [
  WorkLogA31,
  WorkLogA32,
  WorkLogA33,
  WorkLogB31,
  WorkLogB32,
  WorkLogB33
]

function _createWorkLog(
  scanner: User,
  community: Community,
  scanRequests: ScanRequest[]
): ScannerWorkLog {
  return {
    _id: new ObjectId(),
    scanner,
    community,
    commuteTime: 0,
    workingHours: 0,
    workLogDate: new Date(),
    scanRequests: scanRequests.map((scanRequest) => scanRequest._id.toString()),
    spaces: []
  }
}

function _createScanRequest(space: Space): ScanRequest {
  return {
    accessDetails: '',
    availableFrom: new Date(),
    status: ScanRequestStatus.COMPLETED,
    requestedBy: {
      _id: new ObjectId(),
      name: 'John Doe',
      email: '<EMAIL>'
    },
    _id: new ObjectId(),
    community: space.community,
    specialInstructions: '',
    space: space,
    isAutoScan: false
  }
}
