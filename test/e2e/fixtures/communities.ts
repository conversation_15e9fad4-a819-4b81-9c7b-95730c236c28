import {
  Community,
  CommunityPlan,
  CommunityTours
} from '@modules/communities/types/community'
import { ObjectId } from '@modules/communities/types/id'
import { Organization } from '@modules/users/types/organization'
import { OrganizationA, OrganizationB } from './organizations'

export const communityA1 = _createCommunity('CommunityA1', OrganizationA)
export const communityA2 = _createCommunity('CommunityA2', OrganizationA)
export const communityA3 = _createCommunity('CommunityA3', OrganizationA)
export const communityA4Inactive = _createCommunity(
  'CommunityA4Inactive',
  OrganizationA,
  false
)

export const communityB1 = _createCommunity('CommunityB1', OrganizationB)
export const communityB2 = _createCommunity('CommunityB2', OrganizationB)
export const communityB3 = _createCommunity('CommunityB3', OrganizationB)

export const communityB4Inactive = _createCommunity(
  'CommunityB4Inactive',
  OrganizationB,
  false
)

export const allCommunities = [
  communityA1,
  communityA2,
  communityA3,
  communityA4Inactive,
  communityB1,
  communityB2,
  communityB3,
  communityB4Inactive
]

export const allActiveCommunities = [
  communityA1,
  communityA2,
  communityA3,
  communityB1,
  communityB2,
  communityB3
]

export const allCommunitiesOrgA = [communityA1, communityA2, communityA3]
export const allCommunitiesOrgB = [communityB1, communityB2, communityB3]

export const communitiesByOrg = (orgId: string) => {
  return allCommunities.filter((c) => c.organization._id.toString() === orgId)
}

export const activeCommunitiesByOrg = (orgId: string) => {
  return communitiesByOrg(orgId).filter((c) => c.isActive)
}

function _createCommunity(
  name: string,
  organization: Organization,
  isActive: boolean = true
): Community {
  return {
    _id: new ObjectId(),
    name,
    organization,
    leadsEmail: '<EMAIL>',
    address: {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      postalCode: '12345'
    },
    isActive,
    assumptions: {
      avgQACall: 10,
      estimateTourSaveRate: 0.5,
      estimateLaborRate: 100,
      schedulingOverhead: 0.1,
      avgTimeSpentOnsite: 10,
      questionRate: 0.5
    },
    canBypassContactForm: false,
    description: 'Community A',
    displayPriceField: '',
    plan: CommunityPlan.Leasing3D,
    showPrices: false,
    showContactForm: true,
    communityContact: {
      contactName: 'John Doe',
      contactPhone: '1234567890',
      contactInstructions: 'Contact us for more information'
    },
    pointOfInterest: {
      nearbyGrocery: {
        latitude: 10,
        longitude: 10,
        name: 'Grocery'
      }
    },
    tours: {
      [CommunityTours.AGT]: { isAvailable: true },
      [CommunityTours.SGT]: { isAvailable: true }
    }
  }
}
