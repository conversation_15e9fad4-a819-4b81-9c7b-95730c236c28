import { Building } from '@modules/communities/types/building'
import { Community } from '@modules/communities/types/community'
import { Id, ObjectId } from '@modules/communities/types/id'
import { Space } from '@modules/communities/types/space'
import { SpaceType } from '@modules/communities/types/spaceType'
import {
  communityA1,
  communityA2,
  communityA3,
  communityB1,
  communityB2,
  communityB3
} from './communities'

const buildingA1 = _createBuilding('BuildingA1', communityA1)
const buildingA2 = _createBuilding('BuildingA2', communityA2)
const buildingA3 = _createBuilding('BuildingA3', communityA3)
const buildingB1 = _createBuilding('BuildingB1', communityB1)
const buildingB2 = _createBuilding('BuildingB2', communityB2)
const buildingB3 = _createBuilding('BuildingB3', communityB3)

export const NodeA11 = new ObjectId()
export const NodeB11 = new ObjectId()

export const SpaceA11 = _createSpace(
  'UnitA11',
  communityA1,
  buildingA1,
  NodeA11
)
export const SpaceA12 = _createSpace('UnitA12', communityA1, buildingA1)
export const SpaceA13 = _createSpace('UnitA13', communityA1, buildingA1)
export const SpaceA14 = _createSpace('UnitA14', communityA1, buildingA1)

export const SpaceA21 = _createSpace('UnitA21', communityA2, buildingA2)
export const SpaceA22 = _createSpace('UnitA22', communityA2, buildingA2)
export const SpaceA23 = _createSpace('UnitA23', communityA2, buildingA2)
export const SpaceA24 = _createSpace('UnitA24', communityA2, buildingA2)

export const SpaceA31 = _createSpace('UnitA31', communityA3, buildingA3)
export const SpaceA32 = _createSpace('UnitA32', communityA3, buildingA3)
export const SpaceA33 = _createSpace('UnitA33', communityA3, buildingA3)
export const SpaceA34 = _createSpace('UnitA34', communityA3, buildingA3)

export const SpaceB11 = _createSpace(
  'UnitB11',
  communityB1,
  buildingB1,
  NodeB11
)
export const SpaceB12 = _createSpace('UnitB12', communityB1, buildingB1)
export const SpaceB13 = _createSpace('UnitB13', communityB1, buildingB1)
export const SpaceB14 = _createSpace('UnitB14', communityB1, buildingB1)

export const SpaceB21 = _createSpace('UnitB21', communityB2, buildingB2)
export const SpaceB22 = _createSpace('UnitB22', communityB2, buildingB2)
export const SpaceB23 = _createSpace('UnitB23', communityB2, buildingB2)
export const SpaceB24 = _createSpace('UnitB24', communityB2, buildingB2)

export const SpaceB31 = _createSpace('UnitB31', communityB3, buildingB3)
export const SpaceB32 = _createSpace('UnitB32', communityB3, buildingB3)
export const SpaceB33 = _createSpace('UnitB33', communityB3, buildingB3)
export const SpaceB34 = _createSpace('UnitB34', communityB3, buildingB3)

export const allBuildings = [
  buildingA1,
  buildingA2,
  buildingA3,
  buildingB1,
  buildingB2,
  buildingB3
]

export const allSpacesInCommunityA1 = [SpaceA11, SpaceA12, SpaceA13, SpaceA14]

export const allSpaces = [
  SpaceA11,
  SpaceA12,
  SpaceA13,
  SpaceA14,
  SpaceA21,
  SpaceA22,
  SpaceA23,
  SpaceA24,
  SpaceA31,
  SpaceA32,
  SpaceA33,
  SpaceA34,
  SpaceB11,
  SpaceB12,
  SpaceB13,
  SpaceB14,
  SpaceB21,
  SpaceB22,
  SpaceB23,
  SpaceB24,
  SpaceB31,
  SpaceB32,
  SpaceB33,
  SpaceB34
]

export const allSpacesByCommunity = (community: Community) => {
  return allSpaces.filter(
    (s) => s.community._id.toString() === community._id.toString()
  )
}

function _createSpace(
  unit: string,
  community: Community,
  building: Building,
  nodeId?: Id
): Space {
  return {
    _id: new ObjectId(),
    unit,
    community,
    nodes: [
      {
        nodeLinks: [],
        _id: nodeId || new ObjectId(),
        label: 'Kitchen',
        infospots: [],
        captureCount: 1,
        photo: {
          url: 'https://example.com/photo.jpg'
        },
        stagedPhoto: {
          url: 'https://example.com/staged-photo.jpg'
        },
        rotation: {
          pitch: 0,
          roll: 0,
          yaw: 0
        }
      }
    ],
    isMultiRes: false,
    address: {
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      street: '123 Main St'
    },
    building,
    deletedAt: null,
    displayPrice: 1000,
    isComplete: true,
    sgtEnabled: true,
    type: SpaceType.Unit,
    isHorizonLevel: false,
    updatedBy: new ObjectId().toString(),
    createdAt: new Date().toISOString(),
    scanRequested: false,
    grossRent: 1000,
    description: 'Description',
    floorPlan: {
      url: 'https://example.com/floorplan.jpg',
      name: 'Floor Plan'
    }
  }
}

function _createBuilding(name: string, community: Community): Building {
  return {
    _id: new ObjectId(),
    name,
    communityId: community._id,
    address: community.address,
    spaceIds: []
  }
}
