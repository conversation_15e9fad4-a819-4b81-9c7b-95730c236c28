import { expect } from '@jest/globals'
import type { MatcherFunction } from 'expect'
import type { DXSetting } from '@modules/communities/types/dxSetting'
import { DxSettingsSensitivePaths } from '@modules/communities/services/dxSetting'

// Dot notation sensitive paths

function isMatchPath(path: string[]): boolean {
  const currentPath = path.join('.')
  return DxSettingsSensitivePaths.includes(currentPath)
}

function findSensitivePaths(obj: unknown, path: string[] = []): string[] {
  if (typeof obj !== 'object' || obj === null) return []

  const foundPaths: string[] = []

  if (Array.isArray(obj)) {
    for (let i = 0; i < obj.length; i++) {
      foundPaths.push(...findSensitivePaths(obj[i], [...path, String(i)]))
    }
  } else {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = [...path, key]

      if (isMatchPath(currentPath)) {
        foundPaths.push(currentPath.join('.'))
      }

      if (typeof value === 'object' && value !== null) {
        foundPaths.push(...findSensitivePaths(value, currentPath))
      }
    }
  }

  return foundPaths
}

const toContainDxSettingSensitiveData: MatcherFunction<[]> = function (
  actual: Partial<DXSetting>
) {
  const options = {
    comment: 'Object contains sensitive data',
    isNot: this.isNot,
    promise: this.promise
  }

  const foundPaths = findSensitivePaths(actual)
  const pass = foundPaths.length > 0

  const hint = this.utils.matcherHint(
    'toContainDxSettingSensitiveData',
    undefined,
    undefined,
    options
  )

  if (pass) {
    return {
      message: () =>
        `${hint}\n\nSensitive data found at:\n  ${foundPaths.join(
          '\n  '
        )}\n\n` + `Received:\n${this.utils.printReceived(actual)}`,
      pass: true
    }
  } else {
    return {
      message: () =>
        `${hint}\n\nNo sensitive data was found.\n\nReceived:\n${this.utils.printReceived(
          actual
        )}`,
      pass: false
    }
  }
}

expect.extend({
  toContainDxSettingSensitiveData
})

export {}
