import { MongoMemoryServer } from 'mongodb-memory-server'

export default async function setup() {
  const mongo = await MongoMemoryServer.create()
  const uri = mongo.getUri('backend-v3')

  process.env.MONGODB_URL = uri

  globalThis.__MONGOD__ = mongo

  setupRequiredEnv()
}

function setupRequiredEnv() {
  process.env.TZ = 'UTC'
  process.env.AUTH0_CALLBACK_URL = 'peek.us'
  process.env.AUTH0_CLIENT_ID = '123'
  process.env.AUTH0_CLIENT_SECRET = '123'
  process.env.AUTH0_DOMAIN = 'piiq-dev.auth0.com'

  process.env.RENT_CAFE_V2_SECRETS = '123'

  process.env.SESSION_SECRET = 'peek-session-secret'
  process.env.JWT_SECRET = 'verysecret'
  process.env.JWT_EXPIRES = '7d'
  process.env.PARTNERS_API_SALT = '123'
  process.env.PEEK_OPERATIONS_EMAIL = '<EMAIL>'
  process.env.SPACE_CHANGE_TOPIC = 'SPACE_CHANGE_TOPIC'
  process.env.CATEGORIZER_AMENTITY_QUEUE = 'CATEGORIZER_AMENTITY_QUEUE'
  process.env.CATEGORIZER_NODE_QUEUE = 'CATEGORIZER_NODE_QUEUE'
}
