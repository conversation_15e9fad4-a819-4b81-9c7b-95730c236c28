import { generate } from '@core/token'
import { BuildingModel } from '@modules/communities/models/building'
import { CommunityModel } from '@modules/communities/models/community'
import { DXSettingModel } from '@modules/communities/models/dxSetting'
import { ScanRequestModel } from '@modules/communities/models/scanRequest'
import ScannerWorkLogModel from '@modules/communities/models/scannerWorkLog'
import { SpaceModel } from '@modules/communities/models/space'
import { ActionModel } from '@modules/users/models/action'
import { OrganizationModel } from '@modules/users/models/organization'
import { PolicyModel } from '@modules/users/models/policy'
import { RoleModel } from '@modules/users/models/role'
import { UserModel } from '@modules/users/models/user'
import { User } from '@modules/users/types/user'
import { allCommunities } from '../fixtures/communities'
import { OrganizationA, OrganizationB } from '../fixtures/organizations'
import { allScanRequests, allWorkLogs } from '../fixtures/scan-requests'
import { allBuildings, allSpaces } from '../fixtures/spaces'
import {
  actionFull,
  actionRead,
  agentRole,
  allUsers,
  basicRole,
  orgAdminRole,
  policies,
  scannerRole,
  superAdminRole
} from '../fixtures/users'
import { allDxSettings } from '../fixtures/dx-settings'

export async function insertOrganizations() {
  await OrganizationModel.insertMany([OrganizationA, OrganizationB])
}

export async function insertPersonas() {
  await ActionModel.insertMany([actionFull, actionRead])

  await RoleModel.insertMany([
    superAdminRole,
    orgAdminRole,
    agentRole,
    scannerRole,
    basicRole
  ])

  await UserModel.insertMany(allUsers)

  await PolicyModel.insertMany(policies)
}

export async function insertCommunities() {
  await CommunityModel.insertMany(allCommunities)
}

export async function insertSpaces() {
  await SpaceModel.insertMany(allSpaces)
}

export async function insertBuildings() {
  await BuildingModel.insertMany(allBuildings)
}

export async function insertDxSettings() {
  await DXSettingModel.insertMany(allDxSettings)
}

export async function insertScanRequests() {
  await ScanRequestModel.insertMany(allScanRequests)
}

export async function insertScannerWorkLogs() {
  await ScannerWorkLogModel.insertMany(allWorkLogs)
}

export async function clearScanRequests() {
  await ScanRequestModel.deleteMany({})
}

export async function clearScannerWorkLogs() {
  await ScannerWorkLogModel.deleteMany({})
}

export async function clearDxSettings() {
  await DXSettingModel.deleteMany({})
}

export async function clearBuildings() {
  await BuildingModel.deleteMany({})
}

export async function clearSpaces() {
  await SpaceModel.deleteMany({})
}

export async function clearCommunities() {
  await CommunityModel.deleteMany({})
}

export async function clearPersonas() {
  await RoleModel.deleteMany({})
  await UserModel.deleteMany({})
  await ActionModel.deleteMany({})
  await RoleModel.deleteMany({})
  await PolicyModel.deleteMany({})
}

export async function clearOrganizations() {
  await OrganizationModel.deleteMany({})
}

export async function clearAll() {
  await clearScanRequests()
  await clearScannerWorkLogs()
  await clearDxSettings()
  await clearBuildings()
  await clearSpaces()
  await clearCommunities()
  await clearOrganizations()
  await clearPersonas()
}

export async function getToken(user: User) {
  const userData = {
    _id: user._id,
    roleId: user.roleId,
    name: user.name,
    email: user.email,
    status: user.status,
    organizations: user.organizations
  }

  const token = generate(userData)

  return token
}
