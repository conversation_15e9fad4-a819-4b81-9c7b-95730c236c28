import { Organization } from '@modules/users/types/organization'
import { User } from '@modules/users/types/user'

export function assertUserSensitiveData(user: User) {
  expect(user.password).toBeUndefined()
  expect(user.googleCredentials?.accessToken).toBeUndefined()
  expect(user.googleCredentials?.refreshToken).toBeUndefined()
}

export function assertOrganizationSensitiveData(
  organization: Organization & { users: Array<User> }
) {
  expect(organization.users?.forEach(assertUserSensitiveData))
  organization.apiKeys?.forEach((apiKey) => {
    expect(apiKey.hash).toBeUndefined()
  })
}

export function assertCommunitySensitiveData(community: any) {
  community.dxSettings?.forEach((dxSetting: any) => {
    expect(dxSetting.service).toBe('yardi')
    expect(dxSetting.yardi).toBeUndefined()
  })

  expect(community.leadsEmail).toBeUndefined()
  expect(community.communityContact?.contactName).toBeUndefined()
  expect(community.communityContact?.contactPhone).toBeUndefined()
  assertOrganizationSensitiveData(
    community.organization as Organization & { users: Array<User> }
  )

  community.buildings?.forEach((b) => {
    b.spaces?.forEach((s) => {
      expect(s.displayPrice).toBeUndefined()
    })
  })
}

export function assertDxSettingsSensitiveData(dxSetting: any) {
  expect(dxSetting.yardi).toBeUndefined()
}
