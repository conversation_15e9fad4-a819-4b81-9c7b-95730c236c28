import * as peekInternalApiClient from '@core/request/peekInternalApiClient'
import * as spaceService from '@modules/communities/services/space'
import * as idempotencyMiddleware from '@api/middlewares/idempotency'

import supertest from 'supertest'

import {
  clearAll,
  getToken,
  insertCommunities,
  insertPersonas,
  insertSpaces
} from './config/utils'

import { connectDb } from '@core/db'
import { IdempotentRequestsModel } from '@core/models/idempotentRequests'
import { SpaceModel } from '@modules/communities/models/space'
import { insertOrganizations } from './config/utils'
import { OrganizationA } from './fixtures/organizations'
import {
  NodeA11,
  NodeB11,
  SpaceA11,
  SpaceA12,
  SpaceB11
} from './fixtures/spaces'
import {
  allNonOrgAdminUsers,
  allNonSuperAdminUsers,
  allOrgAdminUsers,
  UserSuperAdminA
} from './fixtures/users'
import { Space } from '@modules/communities/types/space'

describe('Spaces', () => {
  beforeEach((done) => {
    Promise.allSettled([
      connectDb(),
      insertOrganizations(),
      insertPersonas(),
      insertCommunities(),
      insertSpaces()
    ]).then(() => {
      done()
    })
  })

  afterEach((done) => {
    clearAll().then(() => {
      done()
    })
  })

  describe('Update node', () => {
    it('Anonymous users should not be able to update node', async () => {
      const app = await getApp()

      const res = await supertest(app).put(
        `/spaces/${SpaceA11._id}/nodes/${NodeA11}`
      )
      expect(res.status).toBe(401)
    })
    it('Super admins should be able to update node', async () => {
      const token = await getToken(UserSuperAdminA)
      const app = await getApp()
      const res = await supertest(app)
        .put(`/spaces/${SpaceA11._id}/nodes/${NodeA11}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          label: 'New Kitchen'
        })
      expect(res.status).toBe(200)

      expect(res.body.nodes.length).toBe(1)
      expect(res.body.nodes[0]._id).toBe(NodeA11.toString())
      expect(res.body.nodes[0].label).toBe('New Kitchen')
    })

    it.each(allOrgAdminUsers.map((u) => [u.name, u]))(
      '%s should be able to update a node in its own organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceA11 : SpaceB11
        const node =
          user.organizations[0]._id === OrganizationA._id ? NodeA11 : NodeB11

        const token = await getToken(user)
        const app = await getApp()
        const res = await supertest(app)
          .put(`/spaces/${space._id}/nodes/${node}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            label: 'New Kitchen'
          })

        expect(res.status).toBe(200)
        expect(res.body.nodes[0]._id).toBe(node.toString())
        expect(res.body.nodes[0].label).toBe('New Kitchen')
      }
    )

    it.each(allNonOrgAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to update a node in its own organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceA11 : SpaceB11
        const node =
          user.organizations[0]._id === OrganizationA._id ? NodeA11 : NodeB11

        const token = await getToken(user)
        const app = await getApp()
        const res = await supertest(app)
          .put(`/spaces/${space._id}/nodes/${node}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            photo: {
              url: 'https://a-very-very-weird-image-possibly-illegal.jpg'
            }
          })

        expect(res.status).toBe(403)
      }
    )
    it.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to update a node in a different organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceB11 : SpaceA11
        const node =
          user.organizations[0]._id === OrganizationA._id ? NodeB11 : NodeA11

        const token = await getToken(user)
        const app = await getApp()
        const res = await supertest(app)
          .put(`/spaces/${space._id}/nodes/${node}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            photo: {
              url: 'https://a-very-very-weird-image-possibly-illegal.jpg'
            }
          })

        expect(res.status).toBe(403)
      }
    )
  })
  describe('Update space', () => {
    it('Anonymous users should not be able to update space', async () => {
      const app = await getApp()
      const res = await supertest(app).put(`/spaces/${SpaceA11._id}`)
      expect(res.status).toBe(401)
    })
    it('Super admins should be able to update space', async () => {
      const token = await getToken(UserSuperAdminA)
      const app = await getApp()
      const res = await supertest(app)
        .put(`/spaces/${SpaceA11._id}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          isVisible: false
        })
      expect(res.status).toBe(200)
      expect(res.body.isVisible).toBe(false)
    })
    it.each(allOrgAdminUsers.map((u) => [u.name, u]))(
      '%s should be able to update a space in its own organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceA11 : SpaceB11
        const token = await getToken(user)
        const app = await getApp()
        const res = await supertest(app)
          .put(`/spaces/${space._id}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            isVisible: false
          })
        expect(res.status).toBe(200)
        expect(res.body.isVisible).toBe(false)
      }
    )
    it.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to update a space in a different organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceB11 : SpaceA11
        const token = await getToken(user)
        const app = await getApp()
        const res = await supertest(app)
          .put(`/spaces/${space._id}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            isVisible: false
          })
        expect(res.status).toBe(403)
      }
    )

    describe('Idempotency', () => {
      beforeEach(async () => {
        jest.restoreAllMocks()
        await IdempotentRequestsModel.deleteMany({})
      })
      it('Two requests with the same body back to back should return the same response and side effects should be called only once', async () => {
        const payload = {
          nodes: [
            {
              _id: NodeA11,
              label: 'New Kitchen'
            }
          ]
        }
        const internalApiRequestMock = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockResolvedValue({})

        const app = await getApp()
        const token = await getToken(UserSuperAdminA)

        const res1 = await supertest(app)
          .put(`/spaces/${SpaceA11._id}?processImage=true`)
          .set('Authorization', `Bearer ${token}`)
          .set('x-idempotency-key', 'same-body-back-to-back')
          .send(payload)

        const res2 = await supertest(app)
          .put(`/spaces/${SpaceA11._id}?processImage=true`)
          .set('Authorization', `Bearer ${token}`)
          .set('x-idempotency-key', 'same-body-back-to-back')
          .send(payload)

        expect(res1.status).toBe(200)
        expect(res2.status).toBe(200)
        expect(res1.body).toEqual(res2.body)
        expect(internalApiRequestMock).toHaveBeenCalledTimes(1)

        const idempotentRequests = await IdempotentRequestsModel.findOne({
          idempotencyKey: 'same-body-back-to-back'
        })

        expect(idempotentRequests?.responseBody).toEqual(
          JSON.stringify(res1.body)
        )
        expect(idempotentRequests?.responseStatus).toEqual(200)
        expect(res1.headers).toMatchObject(
          JSON.parse(idempotentRequests?.responseHeaders)
        )
      })

      it('Two requests with the same body happen in parallel should return 425 on the second request', async () => {
        const payload = {
          nodes: [
            {
              _id: NodeA11,
              label: 'New Kitchen'
            }
          ]
        }
        const internalApiRequestMock = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockImplementationOnce(
            () => new Promise((resolve) => setTimeout(() => resolve({}), 500))
          )

        const sendHookMock = jest
          .spyOn(idempotencyMiddleware, 'sendHook')
          .mockResolvedValue({})

        const mod = await import('@api/app')
        const app = mod.app
        const token = await getToken(UserSuperAdminA)
        const responses = await Promise.allSettled([
          supertest(app)
            .put(`/spaces/${SpaceA11._id}?processImage=true`)
            .set('Authorization', `Bearer ${token}`)
            .set('x-idempotency-key', 'same-body-in-parallel')
            .send(payload),
          new Promise((resolve) => {
            setTimeout(() => {
              supertest(app)
                .put(`/spaces/${SpaceA11._id}?processImage=true`)
                .set('Authorization', `Bearer ${token}`)
                .set('x-idempotency-key', 'same-body-in-parallel')
                .send(payload)
                .then(resolve)
            }, 100)
          }) as Promise<supertest.Response>
        ])
        expect(responses[0].status).toBe('fulfilled')
        expect(responses[1].status).toBe('fulfilled')
        const response1 = (
          responses[0] as PromiseFulfilledResult<supertest.Response>
        ).value
        const response2 = (
          responses[1] as PromiseFulfilledResult<supertest.Response>
        ).value
        const statuses = [response1.status, response2.status]
        expect(statuses).toMatchObject([200, 425])
        expect(internalApiRequestMock).toHaveBeenCalledTimes(1)
      })
      it('Two requests with the same body happen in parallel but the first request fails should return 200 for the second request and side effects should be called twice', async () => {
        const payload = {
          nodes: [
            {
              _id: NodeA11,
              label: 'New Kitchen'
            }
          ]
        }
        const internalApiRequestMock = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockResolvedValueOnce({})

        const removeSpacePricesMock = jest
          .spyOn(spaceService, 'removeSpacePrices')
          .mockRejectedValueOnce(new Error('test'))

        const app = await getApp()

        const token = await getToken(UserSuperAdminA)
        const responses = await Promise.allSettled([
          supertest(app)
            .put(`/spaces/${SpaceA11._id}?processImage=true`)
            .set('Authorization', `Bearer ${token}`)
            .set('x-idempotency-key', 'same-body-in-parallel-but-first-fails')
            .send(payload),
          new Promise((resolve) => {
            setTimeout(() => {
              supertest(app)
                .put(`/spaces/${SpaceA11._id}?processImage=true`)
                .set('Authorization', `Bearer ${token}`)
                .set(
                  'x-idempotency-key',
                  'same-body-in-parallel-but-first-fails'
                )
                .send(payload)
                .then(resolve)
            }, 550)
          }) as Promise<supertest.Response>
        ])
        expect(responses[0].status).toBe('fulfilled')
        expect(responses[1].status).toBe('fulfilled')
        const response1 = (
          responses[0] as PromiseFulfilledResult<supertest.Response>
        ).value
        const response2 = (
          responses[1] as PromiseFulfilledResult<supertest.Response>
        ).value
        const statuses = [response1.status, response2.status]
        expect(statuses).toMatchObject([500, 200])
        expect(internalApiRequestMock).toHaveBeenCalledTimes(2)
        expect(removeSpacePricesMock).toHaveBeenCalledTimes(2)
      })
      it('Two requests with different idempotency keys happen in parallel should both go through', async () => {
        const payload = {
          nodes: [
            {
              _id: NodeA11,
              label: 'New Kitchen'
            }
          ]
        }
        const internalApiRequestMock = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockResolvedValue({})

        const app = await getApp()
        const token = await getToken(UserSuperAdminA)
        const responses = await Promise.allSettled([
          supertest(app)
            .put(`/spaces/${SpaceA11._id}?processImage=true`)
            .set('Authorization', `Bearer ${token}`)
            .set(
              'x-idempotency-key',
              'same-body-in-parallel-but-different-idempotency-key'
            )
            .send(payload),
          supertest(app)
            .put(`/spaces/${SpaceA12._id}?processImage=true`)
            .set('Authorization', `Bearer ${token}`)
            .set(
              'x-idempotency-key',
              'same-body-in-parallel-but-different-idempotency-key-2'
            )
            .send(payload)
        ])
        expect(responses[0].status).toBe('fulfilled')
        expect(responses[1].status).toBe('fulfilled')
        const response1 = (
          responses[0] as PromiseFulfilledResult<supertest.Response>
        ).value
        const response2 = (
          responses[1] as PromiseFulfilledResult<supertest.Response>
        ).value
        const statuses = [response1.status, response2.status]
        expect(statuses).toMatchObject([200, 200])
        expect(internalApiRequestMock).toHaveBeenCalledTimes(2)
      })
      it('Two requests with no idempotency keys happen in parallel should both go through and no request should be saved in the database', async () => {
        const payload = {
          nodes: [
            {
              _id: NodeA11,
              label: 'New Kitchen'
            }
          ]
        }
        const internalApiRequestMock = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockResolvedValue({})

        const app = await getApp()

        const token = await getToken(UserSuperAdminA)
        const responses = await Promise.allSettled([
          supertest(app)
            .put(`/spaces/${SpaceA11._id}?processImage=true`)
            .set('Authorization', `Bearer ${token}`)
            .send(payload),
          supertest(app)
            .put(`/spaces/${SpaceA12._id}?processImage=true`)
            .set('Authorization', `Bearer ${token}`)
            .send(payload)
        ])
        expect(responses[0].status).toBe('fulfilled')
        expect(responses[1].status).toBe('fulfilled')
        const response1 = (
          responses[0] as PromiseFulfilledResult<supertest.Response>
        ).value
        const response2 = (
          responses[1] as PromiseFulfilledResult<supertest.Response>
        ).value
        const statuses = [response1.status, response2.status]
        expect(statuses).toMatchObject([200, 200])
        expect(internalApiRequestMock).toHaveBeenCalledTimes(2)
        const requests = await IdempotentRequestsModel.find({})
        expect(requests.length).toBe(0)

        internalApiRequestMock.mockClear()
      })
    })
  })

  describe('Node link position calculation', () => {
    describe('when processImage is true', () => {
      it('should calculate the position of the node links', async () => {
        const token = await getToken(UserSuperAdminA)
        const app = await getApp()
        const res = await supertest(app)
          .put(`/spaces/${SpaceA11._id}?processImage=true`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            nodes: [
              {
                label: 'Kitchen',
                photo: {
                  url: `${SpaceA11._id.toString()}/KITCHEN.jpg`
                },
                nodeLinks: [
                  {
                    rotation: { pitch: 0, yaw: -5.2479353 }
                  },
                  {
                    rotation: { pitch: 0, yaw: 158.1604 }
                  },
                  {
                    rotation: { pitch: 0, yaw: 162.6382 }
                  }
                ]
              }
            ]
          })

        expect(res.status).toBe(200)

        // Taken from spaces that were observed to be correct
        function expectPosition(space: Space) {
          expect(space.nodes[0].nodeLinks[0].position).toEqual({
            x: -477.9879476463937,
            z: 43.90355230261388,
            y: 0
          })
          expect(space.nodes[0].nodeLinks[1].position).toEqual({
            x: 445.54988837549973,
            z: -178.56454566508913,
            y: 0
          })
          expect(space.nodes[0].nodeLinks[2].position).toEqual({
            x: 458.13095598250135,
            z: -143.2341690050227,
            y: 0
          })
        }

        expectPosition(res.body)

        const savedSpace = await SpaceModel.findById(SpaceA11._id)
        expectPosition(savedSpace)
      })
    })
    describe('when processImage is not set', () => {
      it('should not calculate the position of the node links', async () => {
        const token = await getToken(UserSuperAdminA)
        const app = await getApp()
        const res = await supertest(app)
          .put(`/spaces/${SpaceA11._id}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            nodes: [
              {
                label: 'Kitchen',
                photo: {
                  url: `${SpaceA11._id.toString()}/KITCHEN.jpg`
                },
                nodeLinks: [
                  {
                    rotation: { pitch: 0, yaw: -5.2479353 }
                  },
                  {
                    rotation: { pitch: 0, yaw: 158.1604 }
                  },
                  {
                    rotation: { pitch: 0, yaw: 162.6382 }
                  }
                ]
              }
            ]
          })

        expect(res.status).toBe(200)

        expect(res.body.nodes[0].nodeLinks[0].position).not.toBeDefined()
        expect(res.body.nodes[0].nodeLinks[1].position).not.toBeDefined()
        expect(res.body.nodes[0].nodeLinks[2].position).not.toBeDefined()
      })
    })
  })
})

async function getApp() {
  const mod = await import('@api/app')
  const app = mod.app
  return app
}
