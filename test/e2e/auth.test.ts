import { app } from '@api/app'
import supertest from 'supertest'
import { clearPersonas, insertPersonas, getToken } from './config/utils'
import {
  UserAgentA2,
  UserOrgAdminA1,
  UserSuperAdminA,
  actionFull,
  actionRead,
  allNonSuperAdminUsers,
  allUsers,
  policies
} from './fixtures/users'
import { UserModel } from '@modules/users/models/user'
import { PolicyModel } from '@modules/users/models/policy'
import { ActionModel } from '@modules/users/models/action'

describe('Auth', () => {
  beforeAll((done) => {
    insertPersonas().then(done)
  })

  afterAll((done) => {
    clearPersonas().then(done)
  })

  describe('Login', () => {
    it.each(allUsers.map((user) => [`${user.name}`, user]))(
      '%s should be able to login',
      async (_, user) => {
        const res = await supertest(app).post('/auth/login').send({
          email: user.email,
          password: user.name
        })

        expect(res.status).toBe(200)
        expect(res.body.token).toBeDefined()
      }
    )

    describe('Proxy login', () => {
      it('Super admin should be able to login as other user', async () => {
        const token = await getToken(UserSuperAdminA)

        const res = await supertest(app)
          .post('/auth/proxy-login')
          .set('Authorization', `Bearer ${token}`)
          .send({
            userId: UserOrgAdminA1._id.toString()
          })

        expect(res.status).toBe(200)
        expect(res.body.token).toBeDefined()
      })

      describe('Super admin with different policies', () => {
        beforeAll(async () => {
          const action = await ActionModel.insertOne({
            alias: 'insights_access',
            name: 'Insights Access',
            description: 'Insights Access'
          })

          // Recreating all policies to keep known problematic order of policies
          const exitingPolicies = await PolicyModel.find({})
          await PolicyModel.deleteMany({})
          await PolicyModel.insertMany([
            {
              resource: 'users',
              userId: UserSuperAdminA._id,
              allow: [{ _id: action._id, alias: action.alias }]
            },
            ...exitingPolicies
          ])
        })

        afterAll(async () => {
          await PolicyModel.deleteMany({})
          await ActionModel.deleteMany({})

          await ActionModel.insertMany([actionFull, actionRead])
          await PolicyModel.insertMany(policies)
        })

        it('should be able to login as other user', async () => {
          const token = await getToken(UserSuperAdminA)

          const res = await supertest(app)
            .post('/auth/proxy-login')
            .set('Authorization', `Bearer ${token}`)
            .send({
              userId: UserOrgAdminA1._id.toString()
            })

          expect(res.status).toBe(200)
          expect(res.body.token).toBeDefined()
        })
      })
    })

    it.each(allNonSuperAdminUsers.map((user) => [`${user.name}`, user]))(
      'Regular user %s should not be able to login as another user',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post('/auth/proxy-login')
          .set('Authorization', `Bearer ${token}`)
          .send({
            userId: UserAgentA2._id.toString()
          })

        expect(res.status).toBe(403)
      }
    )
  })
})
