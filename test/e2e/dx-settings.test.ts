import { app } from '@api/app'
import supertest from 'supertest'

import { DXSettingModel } from '@modules/communities/models/dxSetting'
import expect from 'expect'
import {
  clearCommunities,
  clearDxSettings,
  clearOrganizations,
  clearPersonas,
  getToken,
  insertCommunities,
  insertDxSettings,
  insertOrganizations,
  insertPersonas
} from './config/utils'
import { communityA1 } from './fixtures/communities'
import {
  allDxSettings,
  dxA1,
  orgADxSettings,
  orgBDxSettings
} from './fixtures/dx-settings'
import { OrganizationA } from './fixtures/organizations'
import {
  allNonOrgAdminUsers,
  allNonSuperAdminUsers,
  allOrgAdminUsers,
  UserOrgAdminA1,
  UserOrgAdminB1,
  UserSuperAdminA
} from './fixtures/users'

describe('DX Settings', () => {
  beforeEach((done) => {
    Promise.allSettled([
      insertOrganizations(),
      insertPersonas(),
      insertCommunities(),
      insertDxSettings()
    ]).then(() => {
      done()
    })
  })

  afterEach((done) => {
    Promise.allSettled([
      clearPersonas(),
      clearOrganizations(),
      clearCommunities(),
      clearDxSettings()
    ]).then(() => {
      done()
    })
  })

  describe('List DX settings', () => {
    it('Anonymous user should not be able to list DX settings', async () => {
      const res = await supertest(app).get('/dx-settings')
      expect(res.statusCode).toBe(401)
    })
    describe('Super admin lists DX settings', () => {
      let res: supertest.Response
      beforeEach(async () => {
        const token = await getToken(UserSuperAdminA)
        res = await supertest(app)
          .get('/dx-settings?limit=50')
          .set('Authorization', `Bearer ${token}`)
      })

      it('should return the DX settings', () => {
        expect(res.statusCode).toBe(200)
        expect(res.body.length).toEqual(allDxSettings.length)

        res.body.forEach((dxSetting: any) => {
          expect(dxSetting.communityId).toBeDefined()
          expect(dxSetting.service).toBeDefined()
          expect(dxSetting[dxSetting.service]).toBeDefined()
          expect(dxSetting.dataExchange).toBeDefined()
        })
      })
    })
    describe.each([UserOrgAdminA1, UserOrgAdminB1].map((u) => [u.name, u]))(
      '%s lists DX settings',
      (_, user) => {
        const orgId = user.organizations[0]._id.toString()
        const orgDxSettings =
          orgId === OrganizationA._id.toString()
            ? orgADxSettings
            : orgBDxSettings
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(user)
          res = await supertest(app)
            .get('/dx-settings?limit=50')
            .set('Authorization', `Bearer ${token}`)
        })

        it('should return the DX settings', () => {
          expect(res.statusCode).toBe(200)
          res.body.forEach((dxSetting: any) => {
            expect(dxSetting.communityId).toBeDefined()
            expect(dxSetting.service).toBeDefined()
            expect(dxSetting[dxSetting.service]).toBeDefined()
            expect(dxSetting.dataExchange).toBeDefined()
          })
        })

        it('should return the DX settings only from the same organization', () => {
          expect(res.body.length).toEqual(orgDxSettings.length)
        })

        it('should not return sensitive data', () => {
          res.body.forEach((dxSetting: any) => {
            expect(dxSetting).not.toContainDxSettingSensitiveData()
          })
        })
      }
    )
    describe.each(allNonOrgAdminUsers.map((u) => [u.name, u]))(
      '%s lists DX settings',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(user)
          res = await supertest(app)
            .get('/dx-settings?limit=50')
            .set('Authorization', `Bearer ${token}`)
        })

        it('should not be able to list DX settings', () => {
          expect(res.statusCode.toString()).toMatch(/40/)
        })
      }
    )
  })

  describe('Get DX setting', () => {
    it('Anonymous user should not be able to get DX setting', async () => {
      const res = await supertest(app).get(
        `/dx-settings/funnel/${dxA1._id.toString()}`
      )
      expect(res.statusCode).toBe(401)
    })

    describe.each(allOrgAdminUsers.map((u) => [u.name, u]))(
      '%s gets DX setting ',
      (_, user) => {
        describe.each(
          (user.organizations[0]._id === OrganizationA._id
            ? orgADxSettings
            : orgBDxSettings
          ).map((dxSetting) => [dxSetting.service, dxSetting])
        )('for service: %s', (_, dxSetting) => {
          let res: supertest.Response
          beforeEach(async () => {
            const token = await getToken(user)
            res = await supertest(app)
              .get(
                `/dx-settings/${dxSetting.service}/${dxSetting._id.toString()}`
              )
              .set('Authorization', `Bearer ${token}`)
          })
          it('should return the DX setting', () => {
            expect(res.statusCode).toBe(200)
            expect(res.body.dxSetting).toBeDefined()
            expect(res.body.dxSetting.service).toBe(dxSetting.service)
            expect(res.body.dxSetting.communityId).toBe(dxSetting.communityId)
            expect(res.body.dxSetting.dataExchange).toBeDefined()

            expect(res.body.dxSetting).not.toContainDxSettingSensitiveData()
          })
        })
      }
    )
  })

  describe('Create DX setting', () => {
    it('Anonymous user should not be able to create DX setting', async () => {
      const res = await supertest(app).post('/dx-settings/funnel').send({
        communityId: communityA1._id.toString(),
        service: 'funnel'
      })
      expect(res.statusCode).toBe(401)
    })

    describe('Super admin creates DX setting', () => {
      let res: supertest.Response
      beforeEach(async () => {
        const token = await getToken(UserSuperAdminA)
        res = await supertest(app)
          .post('/dx-settings/realpage')
          .send({
            communityId: communityA1._id.toString(),
            service: 'realpage'
          })
          .set('Authorization', `Bearer ${token}`)
      })

      it('should return the DX setting', () => {
        expect(res.statusCode).toBe(200)
      })

      it('should create the DX setting', async () => {
        const dxSetting = await DXSettingModel.findOne({
          _id: res.body._id
        })
        expect(dxSetting).toBeDefined()
      })
    })
    it.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to create a DX setting',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post('/dx-settings/funnel')
          .send({
            communityId: communityA1._id.toString(),
            service: 'funnel'
          })
          .set('Authorization', `Bearer ${token}`)

        expect(res.statusCode.toString()).toMatch(/40/)
      }
    )
  })
  describe('Update DX setting', () => {
    it('Anonymous user should not be able to update DX setting', async () => {
      const res = await supertest(app)
        .put(`/dx-settings/funnel/${dxA1._id.toString()}`)
        .send({
          communityId: communityA1._id.toString(),
          service: 'funnel',
          dataExchange: {
            apiKey: '123'
          }
        })
      expect(res.statusCode).toBe(401)
    })

    describe('Super admin updates DX setting', () => {
      let res: supertest.Response
      beforeEach(async () => {
        const token = await getToken(UserSuperAdminA)
        res = await supertest(app)
          .put(`/dx-settings/funnel/${dxA1._id.toString()}`)
          .send({
            communityId: communityA1._id.toString(),
            service: 'funnel',
            dataExchange: {
              syncProspect: false
            }
          })
          .set('Authorization', `Bearer ${token}`)
      })

      it('should return the DX setting', () => {
        expect(res.statusCode).toBe(200)
      })

      it('should update the DX setting', async () => {
        const dxSetting = await DXSettingModel.findOne({
          _id: res.body.dxSetting._id
        })
        expect(dxSetting).toBeDefined()
        expect(dxSetting?.dataExchange.syncProspect).toBe(false)
      })
    })
    it.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to update a DX setting',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/dx-settings/funnel/${dxA1._id.toString()}`)
          .send({
            communityId: communityA1._id.toString(),
            service: 'funnel',
            dataExchange: {
              syncProspect: false
            }
          })
          .set('Authorization', `Bearer ${token}`)

        expect(res.statusCode.toString()).toMatch(/40/)
      }
    )
  })
  describe('Delete DX setting', () => {
    it('Anonymous user should not be able to delete DX setting', async () => {
      const res = await supertest(app).delete(
        `/dx-settings/funnel/${dxA1._id.toString()}`
      )

      expect(res.statusCode).toBe(401)
    })

    describe('Super admin deletes DX setting', () => {
      let res: supertest.Response
      beforeEach(async () => {
        const token = await getToken(UserSuperAdminA)
        res = await supertest(app)
          .delete(`/dx-settings/funnel/${dxA1._id.toString()}`)
          .set('Authorization', `Bearer ${token}`)
      })

      it('should return the DX setting', () => {
        expect(res.statusCode).toBe(200)
      })

      it('should delete the DX setting', async () => {
        const dxSetting = await DXSettingModel.findOne({
          _id: res.body._id
        })
        expect(dxSetting).toBeDefined()
        expect(dxSetting).toBeNull()
      })
    })
    it.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to delete a DX setting',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .delete(`/dx-settings/funnel/${dxA1._id.toString()}`)
          .set('Authorization', `Bearer ${token}`)

        expect(res.statusCode.toString()).toMatch(/40/)
      }
    )
  })
})
