import { app } from '@api/app'
import { ObjectId } from '@modules/communities/types/id'
import { UserModel } from '@modules/users/models/user'
import supertest from 'supertest'
import { assertUserSensitiveData } from './config/assertions'
import {
  clearOrganizations,
  clearPersonas,
  getToken,
  insertOrganizations,
  insertPersonas
} from './config/utils'
import { OrganizationA, OrganizationB } from './fixtures/organizations'
import {
  agentRole,
  allNonOrgAdminUsers,
  allNonSuperAdminUsers,
  allOrgAdminUsers,
  allUsers,
  allUsersInOrgA,
  allUsersInOrgB,
  basicRole,
  orgAdminRole,
  scannerRole,
  superAdminRole,
  UserAgentA2,
  UserAgentB2,
  UserOrgAdminA1,
  UserOrgAdminB1,
  UserScannerA3,
  UserScannerB3,
  UserSuperAdminA
} from './fixtures/users'

describe('Users', () => {
  beforeEach((done) => {
    Promise.allSettled([insertOrganizations(), insertPersonas()]).then(() => {
      done()
    })
  })

  afterEach((done) => {
    Promise.allSettled([clearPersonas(), clearOrganizations()]).then(() => {
      done()
    })
  })
  describe('Create users', () => {
    describe('Super admins creates another super admin', () => {
      let res: supertest.Response
      beforeEach(async () => {
        const token = await getToken(UserSuperAdminA)
        res = await supertest(app)
          .post('/users')
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'New Super Admin',
            email: '<EMAIL>',
            roleId: superAdminRole._id.toString()
          })
      })

      it('should return the user', async () => {
        expect(res.status).toBe(200)
        expect(res.body._id).toBeDefined()
        expect(res.body.roleId.toString()).toBe(superAdminRole._id.toString())
      })
      it('should not return sensitive data', () => {
        assertUserSensitiveData(res.body)
      })
      it('should create the user', async () => {
        const newUser = await UserModel.findById(res.body._id)
        expect(newUser).toBeDefined()

        expect(newUser?.roleId.toString()).toBe(superAdminRole._id.toString())
      })
    })

    it.each(allNonSuperAdminUsers.map((user) => [`${user.name}`, user]))(
      '%s should not be able to create a super admin',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post('/users')
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'New Super Admin',
            email: `new-super-admin-${user.name}@example.com`,
            roleId: superAdminRole._id.toString()
          })

        expect(res.status.toString()).toMatch(/40/)
      }
    )

    it.each(allNonSuperAdminUsers.map((user) => [`${user.name}`, user]))(
      '%s should not be able to create a super admin within an organization',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post('/users')
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'New Super Admin',
            email: `new-super-admin-within-org-${user.name}@example.com`,
            roleId: superAdminRole._id.toString(),
            organizations: [OrganizationA._id.toString()]
          })

        expect(res.status).toBe(403)
      }
    )

    describe.each(
      [orgAdminRole, agentRole, scannerRole, basicRole].map((role) => [
        `${role.alias}`,
        role
      ])
    )('Org admins creates a user of type %s in its organization', (_, role) => {
      let res: supertest.Response
      beforeEach(async () => {
        const token = await getToken(UserOrgAdminA1)
        res = await supertest(app)
          .post('/users')
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'New User',
            email: `new-user${role.alias}@example.com`,
            roleId: role._id.toString(),
            organizations: [OrganizationA._id.toString()]
          })
      })

      it('should return the user', () => {
        expect(res.status).toBe(200)
        expect(res.body._id).toBeDefined()
        expect(res.body.roleId.toString()).toBe(role._id.toString())
      })

      it('should not return sensitive data', () => {
        assertUserSensitiveData(res.body)
      })

      it('should create the user', async () => {
        const newUser = await UserModel.findById(res.body._id)
        expect(newUser).toBeDefined()

        expect(newUser?.roleId.toString()).toBe(role._id.toString())
      })
    })

    it.each([UserAgentA2, UserScannerA3].map((user) => [`${user.name}`, user]))(
      '%s should not be able to create a user in its organization',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post('/users')
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'New User',
            email: `new-user-from-non-admin${user.name}@example.com`,
            roleId: agentRole._id.toString(),
            organizations: [OrganizationA._id.toString()]
          })

        expect(res.status).toBe(403)
      }
    )
    it.each([UserAgentB2, UserScannerB3].map((user) => [`${user.name}`, user]))(
      '%s should not be able to create a user in its organization',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post('/users')
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'New User',
            email: `new-user-from-non-admin${user.name}@example.com`,
            roleId: agentRole._id.toString(),
            organizations: [OrganizationB._id.toString()]
          })

        expect(res.status).toBe(403)
      }
    )

    describe.each(
      [orgAdminRole, agentRole, scannerRole, basicRole].map((role) => [
        `${role.alias}`,
        role
      ])
    )(
      'Super admins creates a user of type %s in any organization',
      (_, role) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserOrgAdminA1)
          res = await supertest(app)
            .post('/users')
            .set('Authorization', `Bearer ${token}`)
            .send({
              name: 'New User',
              email: `new-user-from-super-admin${role.alias}@example.com`,
              roleId: role._id.toString(),
              organizations: [OrganizationA._id.toString()]
            })
        })

        it('should return the user', () => {
          expect(res.status).toBe(200)
          expect(res.body._id).toBeDefined()
          expect(res.body.roleId.toString()).toBe(role._id.toString())
        })

        it('should not return sensitive data', () => {
          assertUserSensitiveData(res.body)
        })
      }
    )

    it.each(
      [orgAdminRole, agentRole, scannerRole, basicRole].map((role) => [
        `${role.alias}`,
        role
      ])
    )(
      'UserOrgAdminA1 should not be able to create a user of type %s in Org B',
      async (_, role) => {
        const token = await getToken(UserOrgAdminA1)
        const res = await supertest(app)
          .post('/users')
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'New User',
            email: `new-user-from-org-admin-a1${role.alias}@example.com`,
            roleId: role._id.toString(),
            organizations: [OrganizationB._id.toString()]
          })

        expect(res.status).toBe(403)
      }
    )

    it.each(
      [orgAdminRole, agentRole, scannerRole, basicRole].map((role) => [
        `${role.alias}`,
        role
      ])
    )(
      'UserOrgAdminB1 should not be able to create a user of type %s in Org A',
      async (_, role) => {
        const token = await getToken(UserOrgAdminB1)
        const res = await supertest(app)
          .post('/users')
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'New User',
            email: `new-user-from-org-admin-b1${role.alias}@example.com`,
            roleId: role._id.toString(),
            organizations: [OrganizationA._id.toString()]
          })

        expect(res.status).toBe(403)
      }
    )

    it.each(
      [superAdminRole, orgAdminRole, agentRole, scannerRole, basicRole].map(
        (role) => [`${role.alias}`, role]
      )
    )(
      'Anonymous users should not be able to create a user of type %s',
      async (_, role) => {
        const res = await supertest(app)
          .post('/users')
          .send({
            name: 'New User',
            email: `new-user-from-anonymous${role.alias}@example.com`,
            roleId: role._id.toString(),
            organizations: [OrganizationA._id.toString()]
          })

        expect(res.status).toBe(401)
      }
    )
  })

  describe('List users', () => {
    describe('Super admins lists all users', () => {
      let res: supertest.Response
      const deletedUserId = new ObjectId()
      beforeEach((done) => {
        UserModel.insertOne({
          ...UserAgentA2,
          email: '<EMAIL>',
          _id: deletedUserId,
          deletedAt: new Date()
        }).then(() => {
          getToken(UserSuperAdminA).then((token) => {
            supertest(app)
              .get('/users')
              .set('Authorization', `Bearer ${token}`)
              .then((r) => {
                res = r
                done()
              })
          })
        })
      })

      it('should return all users', () => {
        expect(res.status).toBe(200)
        expect(res.body.data.length).toBe(allUsers.length)
      })

      it('should not return sensitive data', () => {
        res.body.data.forEach(assertUserSensitiveData)
      })

      it('should not return deleted users', () => {
        expect(
          res.body.data.find(
            (u) => u._id.toString() === deletedUserId.toString()
          )
        ).toBeUndefined()
      })
    })
    describe.each(allOrgAdminUsers.map((user) => [`${user.name}`, user]))(
      'Org admin %s lists its organization users',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(user)
          res = await supertest(app)
            .get('/users')
            .set('Authorization', `Bearer ${token}`)
        })

        it('should return all org users', () => {
          const orgUsers = allUsers.filter(
            (u) =>
              u.organizations[0]?._id.toString() ===
              user.organizations[0]._id.toString()
          )
          expect(res.status).toBe(200)
          expect(res.body.data.length).toBe(orgUsers.length)
          expect(res.body.data.map((u) => u._id.toString()).sort()).toEqual(
            orgUsers.map((u) => u._id.toString()).sort()
          )
        })

        it('should not return sensitive data', () => {
          res.body.data.forEach(assertUserSensitiveData)
        })
      }
    )
    describe.each(
      [UserAgentA2, UserAgentB2].map((user) => [`${user.name}`, user])
    )('Agent %s lists its organization users', (_, user) => {
      let res: supertest.Response
      beforeEach(async () => {
        const token = await getToken(user)
        res = await supertest(app)
          .get('/users')
          .set('Authorization', `Bearer ${token}`)
      })

      it('should return all org users', () => {
        const orgUsers = allUsers.filter(
          (u) =>
            u.organizations[0]?._id.toString() ===
            user.organizations[0]._id.toString()
        )
        expect(res.status).toBe(200)
        expect(res.body.data.length).toBe(orgUsers.length)
        expect(res.body.data.map((u) => u._id.toString()).sort()).toEqual(
          orgUsers.map((u) => u._id.toString()).sort()
        )
      })

      it('should not return sensitive data', () => {
        res.body.data.forEach(assertUserSensitiveData)
      })
    })
    it('Anonymous users should not be able to list users', async () => {
      const res = await supertest(app).get('/users')
      expect(res.status).toBe(401)
    })
  })

  describe('Get user', () => {
    describe.each(allUsers.map((user) => [`${user.name}`, user]))(
      '%s gets their own user',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(user)
          res = await supertest(app)
            .get('/users/__current')
            .set('Authorization', `Bearer ${token}`)
        })

        it('should return the user', () => {
          expect(res.status).toBe(200)
          expect(res.body._id).toBe(user._id.toString())
        })

        it('should not return sensitive data', () => {
          assertUserSensitiveData(res.body)
        })
      }
    )

    describe.each(allNonSuperAdminUsers.map((user) => [`${user.name}`, user]))(
      'Super admin gets %s',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserSuperAdminA)
          res = await supertest(app)
            .get(`/users/${user._id.toString()}`)
            .set('Authorization', `Bearer ${token}`)
        })

        it('should return the user', () => {
          expect(res.status).toBe(200)
          expect(res.body.user._id).toBe(user._id.toString())
        })

        it('should not return sensitive data', () => {
          assertUserSensitiveData(res.body)
        })
      }
    )

    describe.each(allUsersInOrgA.map((user) => [user.name, user]))(
      'Org admin gets %s in Org A',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserOrgAdminA1)

          res = await supertest(app)
            .get(`/users/${user._id.toString()}`)
            .set('Authorization', `Bearer ${token}`)
        })

        it('should return the user', () => {
          expect(res.status).toBe(200)
          expect(res.body.user._id).toBe(user._id.toString())
        })

        it('should not return sensitive data', () => {
          assertUserSensitiveData(res.body)
        })
      }
    )

    describe.each(allUsersInOrgB.map((user) => [user.name, user]))(
      'Org admin gets %s in Org B',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserOrgAdminB1)

          res = await supertest(app)
            .get(`/users/${user._id.toString()}`)
            .set('Authorization', `Bearer ${token}`)
        })

        it('should return the user', () => {
          expect(res.status).toBe(200)
          expect(res.body.user._id).toBe(user._id.toString())
        })

        it('should not return sensitive data', () => {
          assertUserSensitiveData(res.body)
        })
      }
    )
    it.each(allUsersInOrgB.map((user) => [user.name, user]))(
      'Org A admin should not be able to get %s in Org B',
      async (_, user) => {
        const token = await getToken(UserOrgAdminA1)

        const res = await supertest(app)
          .get(`/users/${user._id.toString()}`)
          .set('Authorization', `Bearer ${token}`)

        expect(res.status).toBe(403)
      }
    )
    it('Anonymous users should not be able to get any user', async () => {
      const res = await supertest(app).get(
        `/users/${UserAgentA2._id.toString()}`
      )
      expect(res.status).toBe(401)
    })
  })

  describe('Delete user', () => {
    it('Super users should be able to delete any user', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .delete(`/users/${UserAgentA2._id.toString()}`)
        .set('Authorization', `Bearer ${token}`)

      expect(res.status).toBe(200)
      const user = await UserModel.findById(UserAgentA2._id).setOptions({
        includeSoftDeleted: true
      })

      expect(user?.deletedAt).toBeDefined()
    })

    it.each([UserOrgAdminA1, UserOrgAdminB1].map((user) => [user.name, user]))(
      '%s should be able to delete users in their organization',
      async (_, user) => {
        const token = await getToken(user)
        const userToDelete = allUsers.find(
          (u) =>
            u.organizations[0]?._id.toString() ===
            user.organizations[0]._id.toString()
        )
        const res = await supertest(app)
          .delete(`/users/${userToDelete?._id.toString()}`)
          .set('Authorization', `Bearer ${token}`)

        expect(res.status).toBe(200)
        const deletedUser = await UserModel.findById(
          userToDelete?._id
        ).setOptions({
          includeSoftDeleted: true
        })
        expect(deletedUser?.deletedAt).toBeDefined()
      }
    )
    it.each(allNonOrgAdminUsers.map((user) => [user.name, user]))(
      '%s should not be able to delete users in their organization',
      async (_, user) => {
        const token = await getToken(user)
        const userToDelete = allUsers.find(
          (u) =>
            u.organizations[0]?._id.toString() ===
            user.organizations[0]._id.toString()
        )
        const res = await supertest(app)
          .delete(`/users/${userToDelete?._id.toString()}`)
          .set('Authorization', `Bearer ${token}`)

        expect(res.status).toBe(403)
      }
    )
    it('UserOrgAdminA1 should not be able to delete users in Org B', async () => {
      const token = await getToken(UserOrgAdminA1)
      const res = await supertest(app)
        .delete(`/users/${UserAgentB2._id.toString()}`)
        .set('Authorization', `Bearer ${token}`)

      expect(res.status).toBe(403)
    })
    it('Anonymous users should not be able to delete any user', async () => {
      const res = await supertest(app).delete(
        `/users/${UserAgentA2._id.toString()}`
      )
      expect(res.status).toBe(401)
    })
  })

  describe('Update user', () => {
    describe.each(allUsers.map((user) => [user.name, user]))(
      'Super admins updates %s',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserSuperAdminA)
          res = await supertest(app)
            .put(`/users/${user._id.toString()}`)
            .set('Authorization', `Bearer ${token}`)
            .send({
              name: 'New Name'
            })
        })

        it('should return the user', () => {
          expect(res.status).toBe(200)
          expect(res.body._id).toBe(user._id.toString())
        })

        it('should not return sensitive data', () => {
          assertUserSensitiveData(res.body)
        })

        it('should update the user', async () => {
          const updatedUser = await UserModel.findById(user._id).setOptions({
            includeSoftDeleted: true
          })
          expect(updatedUser?.name).toBe('New Name')
        })
      }
    )

    describe.each(allUsersInOrgA.map((user) => [user.name, user]))(
      'Org admins updates %s',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserOrgAdminA1)
          res = await supertest(app)
            .put(`/users/${user._id.toString()}`)
            .set('Authorization', `Bearer ${token}`)
            .send({
              name: 'New Name'
            })
        })

        it('should return the user', () => {
          expect(res.status).toBe(200)
          expect(res.body._id).toBe(user._id.toString())
        })

        it('should not return sensitive data', () => {
          assertUserSensitiveData(res.body)
        })

        it('should update the user', async () => {
          const updatedUser = await UserModel.findById(user._id).setOptions({
            includeSoftDeleted: true
          })
          expect(updatedUser?.name).toBe('New Name')
        })
      }
    )
    describe.each(allUsersInOrgB.map((user) => [user.name, user]))(
      'Org admins updates %s in Org B',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserOrgAdminB1)
          res = await supertest(app)
            .put(`/users/${user._id.toString()}`)
            .set('Authorization', `Bearer ${token}`)
            .send({
              name: 'New Name'
            })
        })

        it('should return the user', () => {
          expect(res.status).toBe(200)
          expect(res.body._id).toBe(user._id.toString())
        })

        it('should not return sensitive data', () => {
          assertUserSensitiveData(res.body)
        })

        it('should update the user', async () => {
          const updatedUser = await UserModel.findById(user._id).setOptions({
            includeSoftDeleted: true
          })
          expect(updatedUser?.name).toBe('New Name')
        })
      }
    )
    describe.each(allUsers.map((user) => [user.name, user]))(
      '%s should be able to update their own user',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(user)
          res = await supertest(app)
            .put(`/users/${user._id.toString()}`)
            .set('Authorization', `Bearer ${token}`)
            .send({
              name: 'New Name'
            })
        })

        it('should return the user', () => {
          expect(res.status).toBe(200)
          expect(res.body._id).toBe(user._id.toString())
        })

        it('should not return sensitive data', () => {
          assertUserSensitiveData(res.body)
        })

        it('should update the user', async () => {
          const updatedUser = await UserModel.findById(user._id).setOptions({
            includeSoftDeleted: true
          })
          expect(updatedUser?.name).toBe('New Name')
        })
      }
    )
    it.each([UserOrgAdminA1, UserOrgAdminB1].map((user) => [user.name, user]))(
      '%s should not be able to update users in other organizations',
      async (_, user) => {
        const userToUpdate = [UserAgentA2, UserAgentB2].find(
          (u) =>
            u.organizations[0]?._id.toString() !==
            user.organizations[0]._id.toString()
        )
        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/users/${userToUpdate?._id.toString()}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'New Name'
          })
        expect(res.status).toBe(403)
      }
    )
    it.each(
      allUsersInOrgA
        .filter((u) => u._id !== UserAgentA2._id)
        .map((user) => [user.name, user])
    )('Non org admins should not be able to update %s', async (_, user) => {
      const token = await getToken(UserAgentA2)
      const res = await supertest(app)
        .put(`/users/${user?._id.toString()}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          name: 'New Name'
        })
      expect(res.status).toBe(403)
    })
    it('Anonymous users should not be able to update any user', async () => {
      const res = await supertest(app)
        .put(`/users/${UserAgentA2._id.toString()}`)
        .send({
          name: 'New Name'
        })
      expect(res.status).toBe(401)
    })
  })
})
