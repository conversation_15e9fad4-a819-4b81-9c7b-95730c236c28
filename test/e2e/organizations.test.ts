import { app } from '@api/app'
import supertest from 'supertest'
import { assertOrganizationSensitiveData } from './config/assertions'
import {
  clearOrganizations,
  clearPersonas,
  getToken,
  insertOrganizations,
  insertPersonas
} from './config/utils'
import { OrganizationA, OrganizationB } from './fixtures/organizations'
import {
  allNonOrgAdminUsers,
  allNonSuperAdminUsers,
  UserOrgAdminA1,
  UserOrgAdminB1,
  UserSuperAdminA
} from './fixtures/users'
import { OrganizationModel } from '@modules/users/models/organization'

describe('Organizations', () => {
  beforeEach((done) => {
    Promise.allSettled([insertOrganizations(), insertPersonas()]).then(() => {
      done()
    })
  })

  afterEach((done) => {
    Promise.allSettled([clearPersonas(), clearOrganizations()]).then(() => {
      done()
    })
  })

  describe('List organizations', () => {
    it('Anonymous users should not be able to list organizations', async () => {
      const res = await supertest(app).get('/organizations').query({
        domains: OrganizationA.domains[0]
      })
      expect(res.status).toBe(401)
    })

    it('Super admins should be able to list organizations', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .get('/organizations')
        .set('Authorization', `Bearer ${token}`)

      expect(res.status).toBe(200)
      expect(res.body.data.length).toBe(2)

      const orgNames = res.body.data.map((org) => org.name)

      expect(orgNames).toContain(OrganizationA.name)
      expect(orgNames).toContain(OrganizationB.name)
    })

    describe('Org admins list organizations', () => {
      let res: supertest.Response
      beforeEach(async () => {
        const token = await getToken(UserOrgAdminA1)
        res = await supertest(app)
          .get('/organizations')
          .set('Authorization', `Bearer ${token}`)
      })

      it('should return only its own organization', () => {
        expect(res.status).toBe(200)
        expect(res.body.data.length).toBe(1)

        expect(res.body.data[0].name).toBe(OrganizationA.name)
      })

      it('should not include sensitive data', () => {
        res.body.data.forEach(assertOrganizationSensitiveData)
      })
    })
    it.each(allNonOrgAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to list organizations',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .get('/organizations')
          .set('Authorization', `Bearer ${token}`)

        expect(res.status).toBe(403)
      }
    )
  })
  describe('Create partner api key', () => {
    it('Anonymous users should not be able to create partner api key', async () => {
      const res = await supertest(app)
        .post(`/organizations/${OrganizationA._id}/partners-api-key`)
        .send({
          name: 'API Key 1',
          expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30)
        })

      expect(res.status).toBe(401)
    })

    it('Super admins should be able to create partner api key', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .post(`/organizations/${OrganizationA._id}/partners-api-key`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          name: 'API Key 1',
          expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30)
        })

      expect(res.status).toBe(200)

      const updatedOrganization = await OrganizationModel.findById(
        OrganizationA._id
      )
      expect(updatedOrganization?.apiKeys).toHaveLength(2)
    })

    it.each([UserOrgAdminA1, UserOrgAdminB1].map((u) => [u.name, u]))(
      '%s should be able to create partner api key for its own organization',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post(`/organizations/${user.organizations[0]._id}/partners-api-key`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'API Key 1',
            expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30)
          })

        expect(res.status).toBe(200)
        const updatedOrganization = await OrganizationModel.findById(
          user.organizations[0]._id
        )
        expect(updatedOrganization?.apiKeys).toHaveLength(2)
      }
    )
    it.each([UserOrgAdminA1, UserOrgAdminB1].map((u) => [u.name, u]))(
      '%s should not be able to create partner api key for other organizations',
      async (_, user) => {
        const token = await getToken(user)
        const otherOrganization =
          user.organizations[0]._id === OrganizationA._id
            ? OrganizationB
            : OrganizationA
        const res = await supertest(app)
          .post(`/organizations/${otherOrganization._id}/partners-api-key`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'API Key 1',
            expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30)
          })

        expect(res.status).toBe(403)
      }
    )
    it.each(allNonOrgAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to create partner api key',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post(`/organizations/${OrganizationA._id}/partners-api-key`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'API Key 1',
            expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30)
          })

        expect(res.status).toBe(403)
      }
    )
  })
  describe('Create organization', () => {
    it('Anonymous users should not be able to create organization', async () => {
      const res = await supertest(app).post('/organizations').send({
        name: 'Organization 1'
      })
      expect(res.status).toBe(401)
    })

    it('Super admins should be able to create organization', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .post('/organizations')
        .set('Authorization', `Bearer ${token}`)
        .send({
          name: 'Organization 1',
          domains: ['organization1.com']
        })

      expect(res.status).toBe(200)

      const updatedOrganization = await OrganizationModel.findById(res.body._id)
      expect(updatedOrganization?.name).toBe('Organization 1')
      expect(updatedOrganization?.domains).toEqual(['organization1.com'])
    })

    it.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to create organization',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post('/organizations')
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'Organization 1',
            domains: ['organization1.com']
          })

        expect(res.status).toBe(403)
      }
    )
  })
  describe('Update organization', () => {
    it('Anonymous users should not be able to update organization', async () => {
      const res = await supertest(app)
        .put(`/organizations/${OrganizationA._id}`)
        .send({
          name: 'Organization 1'
        })
      expect(res.status).toBe(401)
    })

    it('Super admins should be able to update organization', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .put(`/organizations/${OrganizationA._id}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          name: 'Organization 1'
        })

      expect(res.status).toBe(200)

      const updatedOrganization = await OrganizationModel.findById(
        OrganizationA._id
      )
      expect(updatedOrganization?.name).toBe('Organization 1')
    })

    it.each([UserOrgAdminA1, UserOrgAdminB1].map((u) => [u.name, u]))(
      '%s should be able to update their own organization',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/organizations/${user.organizations[0]._id}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'Organization 1'
          })

        expect(res.status).toBe(200)

        const updatedOrganization = await OrganizationModel.findById(
          user.organizations[0]._id
        )
        expect(updatedOrganization?.name).toBe('Organization 1')
      }
    )
    it.each([UserOrgAdminA1, UserOrgAdminB1].map((u) => [u.name, u]))(
      '%s should not be able to update other organizations',
      async (_, user) => {
        const organization =
          user.organizations[0]._id === OrganizationA._id
            ? OrganizationB
            : OrganizationA
        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/organizations/${organization._id}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'Organization 1'
          })

        expect(res.status).toBe(403)
      }
    )
    it.each(allNonOrgAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to update organization',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/organizations/${OrganizationA._id}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            name: 'Organization 1'
          })

        expect(res.status).toBe(403)
      }
    )
  })
})
