import { app } from '@api/app'
import supertest from 'supertest'

import { CommunityModel } from '@modules/communities/models/community'
import { SpaceModel } from '@modules/communities/models/space'
import { PolicyModel } from '@modules/users/models/policy'
import { UserModel } from '@modules/users/models/user'
import { assertCommunitySensitiveData } from './config/assertions'
import {
  clearBuildings,
  clearCommunities,
  clearDxSettings,
  clearOrganizations,
  clearPersonas,
  clearSpaces,
  getToken,
  insertBuildings,
  insertCommunities,
  insertDxSettings,
  insertOrganizations,
  insertPersonas,
  insertSpaces
} from './config/utils'
import {
  activeCommunitiesByOrg,
  allActiveCommunities,
  allCommunities,
  communityA1,
  communityB1
} from './fixtures/communities'
import { OrganizationA, OrganizationB } from './fixtures/organizations'
import { allSpacesByCommunity, allSpacesInCommunityA1 } from './fixtures/spaces'
import {
  allNonSuperAdminUsers,
  allUsersInCommunityA1,
  allUsersInOrgA,
  allUsersInOrgB,
  UserOrgAdminA1,
  UserOrgAdminB1,
  UserSuperAdminA
} from './fixtures/users'

describe('Communities', () => {
  beforeEach((done) => {
    Promise.allSettled([
      insertOrganizations(),
      insertPersonas(),
      insertCommunities(),
      insertSpaces(),
      insertBuildings(),
      insertDxSettings()
    ]).then(() => {
      done()
    })
  })

  afterEach((done) => {
    Promise.allSettled([
      clearPersonas(),
      clearOrganizations(),
      clearCommunities(),
      clearSpaces(),
      clearBuildings(),
      clearDxSettings()
    ]).then(() => {
      done()
    })
  })

  describe('List communities', () => {
    describe('Anonymous users lists communities', () => {
      let res: supertest.Response
      beforeEach(async () => {
        res = await supertest(app).get('/communities')
      })

      it('should return all active communities', () => {
        expect(res.status).toBe(200)
        expect(res.body.data.length).toBe(allActiveCommunities.length)

        expect(res.body.data.map((c) => c.name).sort()).toEqual(
          allActiveCommunities.map((c) => c.name).sort()
        )
      })
      it('should not return sensitive data', () => {
        res.body.data.forEach(assertCommunitySensitiveData)
      })
    })
    describe('Anonymous users lists communities including dx settings', () => {
      let res: supertest.Response
      beforeEach(async () => {
        res = await supertest(app).get('/communities?include=dxSettings')
      })

      it('should not return any dx setting', () => {
        expect(res.status).toBe(200)
        expect(res.body.data.length).toBe(allActiveCommunities.length)

        res.body.data.forEach((c) => expect(c.dxSettings).toBeUndefined())
      })
    })
    it('Super admin users should list all communities', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .get('/communities')
        .set('Authorization', `Bearer ${token}`)

      expect(res.status).toBe(200)
      expect(res.body.data.length).toBe(allCommunities.length)
    })

    describe.each([UserOrgAdminA1, UserOrgAdminB1].map((u) => [u.name, u]))(
      '%s lists communities including spaces',
      (_, user) => {
        const orgId = user.organizations[0]._id.toString()

        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(user)
          res = await supertest(app)
            .get('/communities?include=spaces')
            .set('Authorization', `Bearer ${token}`)
        })

        it('should return its organization communities with spaces', async () => {
          expect(res.status).toBe(200)
          const orgCommunities = res.body.data.filter(
            (c) => c.organization._id === orgId
          )
          expect(orgCommunities.length).toBe(
            activeCommunitiesByOrg(orgId).length
          )

          orgCommunities.forEach((c) => {
            expect(c.buildings.length).toBe(1)

            const spaces = allSpacesByCommunity(c)

            expect(c.buildings[0].spaces.length).toBe(spaces.length)

            c.buildings[0].spaces.forEach((s) => {
              expect(s.unit).toBeDefined()
              expect(s.token).toBeDefined()
            })
          })
        })
        it('should not return sensitive data', () => {
          res.body.data.forEach(assertCommunitySensitiveData)
        })
      }
    )
  })
  describe('Get community', () => {
    describe('Anonymous users get community', () => {
      let res: supertest.Response
      beforeEach(async () => {
        res = await supertest(app).get(`/communities/${communityA1._id}`)
      })

      it('should return the community', () => {
        expect(res.status).toBe(200)
        expect(res.body.name).toBe(communityA1.name)
      })

      it('should not return sensitive data', () => {
        assertCommunitySensitiveData(res.body)
      })
    })
    describe('Anonymous users get community including extra properties', () => {
      let res: supertest.Response
      beforeEach(async () => {
        res = await supertest(app).get(
          `/communities/${communityA1._id}?include=dxSettings,spaces,buildings,organization`
        )
      })

      it('should not include sensitive data', async () => {
        expect(res.status).toBe(200)
        assertCommunitySensitiveData(res.body)
      })
    })
    describe.each([UserOrgAdminA1, UserOrgAdminB1].map((u) => [u.name, u]))(
      '%s get community including extra properties',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(user)
          res = await supertest(app)
            .get(
              `/communities/${communityA1._id}?include=spaces,buildings,organization`
            )
            .set('Authorization', `Bearer ${token}`)
        })

        it('should return the community with spaces and buildings without sensitive data', () => {
          expect(res.status).toBe(200)
          expect(res.body.buildings.length).toBe(1)
          expect(res.body.buildings[0].spaces.length).toBe(
            allSpacesByCommunity(res.body).length
          )

          res.body.buildings[0].spaces.forEach((s) => {
            expect(s.unit).toBeDefined()
            expect(s.token).toBeDefined()
            expect(s.displayPrice).toBeUndefined()
          })
        })
      }
    )
  })
  describe('Create community', () => {
    it('Anonymous user should not be able to create community', async () => {
      const res = await supertest(app).post('/communities')
      expect(res.statusCode).toBe(401)
    })
    it('Super admin user should be able to create community', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .post('/communities')
        .send({
          name: 'Test Community',
          organization: {
            _id: OrganizationA._id.toString(),
            name: OrganizationA.name
          }
        })
        .set('Authorization', `Bearer ${token}`)
      expect(res.statusCode).toBe(200)
      expect(res.body.name).toBe('Test Community')
      expect(res.body.organization._id).toBe(OrganizationA._id.toString())
      expect(res.body.organization.name).toBe(OrganizationA.name)
      assertCommunitySensitiveData(res.body)

      const community = await CommunityModel.findOne({
        name: 'Test Community'
      })
      expect(community).toBeDefined()
    })
    it.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to create community',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .post('/communities')
          .send({
            name: 'Test Community',
            organization: {
              _id: user.organizations[0]._id.toString(),
              name: user.organizations[0].name
            }
          })
          .set('Authorization', `Bearer ${token}`)
        expect(res.statusCode.toString()).toMatch(/40/)
      }
    )
  })
  describe('Update community', () => {
    it('Anonymous user should not be able to update community', async () => {
      const res = await supertest(app).put(`/communities/${communityA1._id}`)
      expect(res.statusCode).toBe(401)
    })
    describe('Super admin updates a community', () => {
      let res: supertest.Response
      beforeEach(async () => {
        const token = await getToken(UserSuperAdminA)
        res = await supertest(app)
          .put(`/communities/${communityA1._id}`)
          .send({ name: 'Updated Community' })
          .set('Authorization', `Bearer ${token}`)
      })

      it('should update the community', () => {
        expect(res.statusCode).toBe(200)
        expect(res.body.name).toBe('Updated Community')
      })

      it('should not return sensitive data', () => {
        assertCommunitySensitiveData(res.body)
      })

      it('should update the community', async () => {
        const community = await CommunityModel.findById(communityA1._id)
        expect(community).toBeDefined()
        expect(community?.name).toBe('Updated Community')
      })
    })
    it.each(allUsersInOrgB.map((u) => [u.name, u]))(
      '%s should not be able to update community in org A',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/communities/${communityA1._id}`)
          .send({
            name: 'Updated Community',
            isActive: false
          })
          .set('Authorization', `Bearer ${token}`)
        expect(res.statusCode.toString()).toMatch(/40/)
      }
    )
    it.each(allUsersInOrgA.map((u) => [u.name, u]))(
      '%s should not be able to update community in org B',
      async (_, user) => {
        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/communities/${communityB1._id}`)
          .send({
            name: 'Updated Community',
            isActive: false
          })
          .set('Authorization', `Bearer ${token}`)
        expect(res.statusCode.toString()).toMatch(/40/)
      }
    )

    describe('Super admin updates a community', () => {
      describe('changing only the name of the community', () => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserSuperAdminA)
          res = await supertest(app)
            .put(`/communities/${communityA1._id}`)
            .set('Authorization', `Bearer ${token}`)
            .send({
              name: 'Updated Community',
              // Sending the same organization. Regression test for #BUG-1684
              organization: { _id: OrganizationA._id.toString() }
            })
        })
        it('should return the new name', async () => {
          expect(res.statusCode).toBe(200)
          expect(res.body.name).toBe('Updated Community')
        })
        it('should update the community name in the community collection', async () => {
          const community = await CommunityModel.findById(communityA1._id)
          expect(community).toBeDefined()
          expect(community?.name).toBe('Updated Community')
        })
        it('should update the community name in the spaces collection', async () => {
          const spaces = await SpaceModel.find({
            'community._id': communityA1._id
          })
          expect(spaces).toBeDefined()
          expect(spaces.length).toBe(allSpacesInCommunityA1.length)
          spaces.forEach((s) => {
            expect(s.community.name).toBe('Updated Community')
          })
        })
        it('should update the community name in the users collection', async () => {
          const users = await UserModel.find({
            'communities._id': communityA1._id
          })
          expect(users).toBeDefined()
          expect(users.length).toBe(allUsersInCommunityA1.length)
          users.forEach((u) => {
            expect(u.communities.length).toBe(1)
            expect(u.communities[0].name).toBe('Updated Community')
          })
        })
        it('should keep the same organization', async () => {
          const community = await CommunityModel.findById(communityA1._id)
          expect(community).toBeDefined()
          expect(community?.organization._id.toString()).toBe(
            OrganizationA._id.toString()
          )
          expect(community?.organization.name).toBe(OrganizationA.name)
        })
        it('should keep the same amount of users with access to the community', async () => {
          const policies = await PolicyModel.find({
            communityId: communityA1._id
          })
          expect(policies).toBeDefined()
          expect(policies.length).toBe(allUsersInCommunityA1.length)
        })
        it('should keep the same amount of spaces in the community', async () => {
          const spaces = await SpaceModel.find({
            'community._id': communityA1._id
          })
          expect(spaces).toBeDefined()
          expect(spaces.length).toBe(allSpacesInCommunityA1.length)
        })
      })
      describe("changing the 'isActive' field", () => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserSuperAdminA)
          res = await supertest(app)
            .put(`/communities/${communityA1._id}`)
            .set('Authorization', `Bearer ${token}`)
            .send({
              isActive: false,
              // Sending the same organization. Regression test for #BUG-1684
              organization: { _id: OrganizationA._id.toString() }
            })
        })
        it('should return the community with the new isActive value', () => {
          expect(res.statusCode).toBe(200)
          expect(res.body.isActive).toBe(false)
        })
        it('should update the community isActive in the community collection', async () => {
          const community = await CommunityModel.findById(communityA1._id)
          expect(community).toBeDefined()
          expect(community?.isActive).toBe(false)
        })
        it('should keep the same organization', async () => {
          const community = await CommunityModel.findById(communityA1._id)
          expect(community).toBeDefined()
          expect(community?.organization._id.toString()).toBe(
            OrganizationA._id.toString()
          )
          expect(community?.organization.name).toBe(OrganizationA.name)
        })
        it('should keep the same amount of users with access to the community', async () => {
          const policies = await PolicyModel.find({
            communityId: communityA1._id
          })
          expect(policies).toBeDefined()
          expect(policies.length).toBe(allUsersInCommunityA1.length)
        })
        it('should not return any space in the newly inactive community', async () => {
          const spaces = await SpaceModel.find({
            'community._id': communityA1._id
          })
          expect(spaces).toBeDefined()
          expect(spaces.length).toBe(0)
        })
      })
      describe('changing the organization', () => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(UserSuperAdminA)
          res = await supertest(app)
            .put(`/communities/${communityA1._id}`)
            .set('Authorization', `Bearer ${token}`)
            .send({ organization: { _id: OrganizationB._id.toString() } })
        })
        it('should return the community with the new organization', () => {
          expect(res.statusCode).toBe(200)
          expect(res.body.organization._id).toBe(OrganizationB._id.toString())
          expect(res.body.organization.name).toBe(OrganizationB.name)
        })
        it('should update the community organization in the community collection', async () => {
          const community = await CommunityModel.findById(communityA1._id)
          expect(community).toBeDefined()
          expect(community?.organization._id.toString()).toBe(
            OrganizationB._id.toString()
          )
          expect(community?.organization.name).toBe(OrganizationB.name)
        })
        it('should update the community organization in the spaces collection', async () => {
          const spaces = await SpaceModel.find({
            'community._id': communityA1._id
          })
          expect(spaces).toBeDefined()
          expect(spaces.length).toBe(allSpacesInCommunityA1.length)
          spaces.forEach((s) => {
            expect(s.community.organization._id.toString()).toBe(
              OrganizationB._id.toString()
            )
            expect(s.community.organization.name).toBe(OrganizationB.name)
          })
        })
        it('should remove users from the old organization', async () => {
          const usersOnA = await UserModel.find({
            'organizations._id': OrganizationA._id,
            'communities._id': communityA1._id
          })
          expect(usersOnA).toBeDefined()
          expect(usersOnA.length).toBe(0)
        })
        it('should add org admins from the new org to the community', async () => {
          const usersOnB = await UserModel.find({
            'organizations._id': OrganizationB._id,
            'communities._id': communityA1._id
          })
          expect(usersOnB).toBeDefined()
          expect(usersOnB.length).toBe(1) // Just the org admin
        })
      })
    })
  })
})
