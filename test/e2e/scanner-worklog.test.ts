import { app } from '@api/app'
import supertest from 'supertest'

import {
  clearAll,
  getToken,
  insertPersonas,
  insertScannerWorkLogs,
  insertScanRequests
} from './config/utils'

import { insertOrganizations } from './config/utils'
import { allWorkLogs } from './fixtures/scan-requests'
import {
  allNonSuperAdminUsers,
  UserScannerA3,
  UserScannerB3,
  UserSuperAdminA
} from './fixtures/users'
import { assertUserSensitiveData } from './config/assertions'

describe('Scanner Worklog', () => {
  beforeEach((done) => {
    Promise.allSettled([
      insertOrganizations(),
      insertPersonas(),
      insertScanRequests(),
      insertScannerWorkLogs()
    ]).then(() => {
      done()
    })
  })

  afterEach((done) => {
    clearAll().then(() => {
      done()
    })
  })

  describe('Get scanner worklog', () => {
    it('Anonymous users should not be able to get scanner worklog', async () => {
      const res = await supertest(app).get('/scanner-work-logs')
      expect(res.status).toBe(401)
    })
    it('Super admins should be able to get scanner worklogs', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .get('/scanner-work-logs')
        .set('Authorization', `Bearer ${token}`)
      expect(res.status).toBe(200)
      expect(res.body.data.data.length).toBe(allWorkLogs.length)
    })

    describe.each([UserScannerA3, UserScannerB3].map((u) => [u.name, u]))(
      '%s gets scanner worklogs',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(user)
          res = await supertest(app)
            .get('/scanner-work-logs')
            .set('Authorization', `Bearer ${token}`)
        })

        it('should return only its own worklog', () => {
          expect(res.status).toBe(200)
          expect(res.body.data.data.length).toBe(
            allWorkLogs.filter(
              (w) => w.scanner._id.toString() === user._id.toString()
            ).length
          )
        })
      }
    )
    describe.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s gets scanner worklogs',
      (_, user) => {
        let res: supertest.Response
        beforeEach(async () => {
          const token = await getToken(user)
          res = await supertest(app)
            .get('/scanner-work-logs')
            .set('Authorization', `Bearer ${token}`)
        })

        it('should not return worklogs from scanners in another organization', () => {
          expect(res.status).toBe(200)
          expect(res.body.data.data.length).toBe(
            allWorkLogs.filter(
              (w) => w.scanner._id.toString() === user._id.toString()
            ).length
          )
        })
        it('should not return sensitive data', () => {
          res.body.data.data.forEach((w) => {
            assertUserSensitiveData(w.scanner)
            expect(w.scanner.email).toBeUndefined()
          })
        })
      }
    )
  })
})
