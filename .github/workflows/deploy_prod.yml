name: Deploy Pipeline Prod

on:
  push:
    tags:
      - "PROD-*"

env:
  NPM_GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  install_dependencies:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest
    name: Install dependencies

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Yarn install
        uses: peek-tech/tools-infra-actions/.github/actions/install@main
        with:
          cache_key: "node_modules"
          npm_token: ${{ secrets.NPM_TOKEN }}

  set_env:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    name: Upload .env file
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Create env file
        run: echo "${{ vars.DOT_ENV_PROD }}" > .env

      - name: Copy env file
        run: aws s3 cp .env s3://peek-environments/peek-backend-v3/prod.env

  build:
    needs: [install_dependencies]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest
    env:
      SENTRY_AUTH_TOKEN: ${{ vars.SENTRY_AUTH_TOKEN }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Yarn build
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        with:
          dependencies_cache_key: "node_modules"
          command: build

      - name: Upload Sentry Sourcemaps
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        with:
          dependencies_cache_key: "node_modules"
          command: sentry:sourcemaps

      - name: Archive dist
        uses: actions/upload-artifact@v4
        with:
          name: dist
          retention-days: 1
          path: |
            ./dist

  push_image:
    name: Push Docker Image to ECR
    needs: [build, set_env]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Checkout
        uses: actions/checkout@v4

      - name: Restore cache
        uses: peek-tech/tools-infra-actions/.github/actions/restore@main
        with:
          cache_key: "node_modules"

      - name: Download dist
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: ./dist

      - name: Build Dockerfile
        run: docker build -t 112470036249.dkr.ecr.us-east-1.amazonaws.com/peek-backend-v3-prod:latest -f Dockerfile.api .

      - name: Authenticate on ECR
        run: aws ecr get-login-password | docker login --username AWS --password-stdin 112470036249.dkr.ecr.us-east-1.amazonaws.com

      - name: Docker push
        run: docker push 112470036249.dkr.ecr.us-east-1.amazonaws.com/peek-backend-v3-prod:latest


  deploy_serverless:
    needs: [build, set_env]
    runs-on: 8_32_runner
    container:
      image: ghcr.io/peek-tech/node20:latest
    strategy:
      matrix:
        config_file:
          [
            serverless.yml,
            serverless-dx.yaml,
            serverless-prospects.yaml,
            serverless-vacancy.yaml
          ]
    name: Deploy ${{ matrix.config_file }}

    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Checkout
        uses: actions/checkout@v4

      - name: Restore cache
        uses: peek-tech/tools-infra-actions/.github/actions/restore@main
        with:
          cache_key: "node_modules"

      - name: Create env file
        run: echo "${{ vars.DOT_ENV_PROD }}" > .env

      - name: Install sls
        run: npm i -g serverless@3.x

      - name: Eslint chmod
        run: chmod -R 777 ./node_modules

      - name: Deploy sls
        run: NODE_OPTIONS="--no-experimental-require-module" sls deploy -s prod --config ${{ matrix.config_file }}

  deploy_api:
    name: Deploy API
    needs: [push_image]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Update ECS Service
        run: aws ecs --region us-east-1 update-service --cluster PROD --service prod-peek-backend-v3 --force-new-deployment

      - name: Wait deployment
        run: for i in {1..3}; do aws ecs wait services-stable --cluster PROD --services prod-peek-backend-v3 && break || echo "Attempt $i failed, retrying..."; done || exit 1
