name: <PERSON><PERSON> Jest E2E Results

on:
  push:
    branches:
      - main

env:
  NPM_GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  install_dependencies:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest
    name: Install dependencies

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Yarn install
        uses: peek-tech/tools-infra-actions/.github/actions/install@main
        with:
          cache_key: "node_modules"
          npm_token: ${{ secrets.NPM_TOKEN }}

  cache-jest:
    needs: [install_dependencies]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Test
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        continue-on-error: true
        with:
          dependencies_cache_key: "node_modules"
          command: test:e2e:json

      - name: <PERSON><PERSON> jest-results.json
        uses: actions/cache@v4
        with:
          path: jest-results.json
          key: jest-results-${{ github.sha }}
