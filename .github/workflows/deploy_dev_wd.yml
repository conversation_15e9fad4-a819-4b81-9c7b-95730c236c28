name: Deploy QA Pipeline

on:
  workflow_dispatch:

env:
  NPM_GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  install_dependencies:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest
    name: Install dependencies

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Yarn install
        uses: peek-tech/tools-infra-actions/.github/actions/install@main
        with:
          cache_key: "node_modules"
          npm_token: ${{ secrets.NPM_TOKEN }}

  set_env:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    name: Upload .env file
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Create env file
        run: echo "${{ vars.DOT_ENV }}" > .env

      - name: Copy env file
        run: aws s3 cp .env s3://peek-environments/peek-backend-v3/dev.env

  build:
    needs: [install_dependencies]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Yarn build
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        with:
          dependencies_cache_key: "node_modules"
          command: build

      - name: Archive dist
        uses: actions/upload-artifact@v4
        with:
          name: dist
          retention-days: 1
          path: |
            ./dist

  push_image:
    name: Push Docker Image to ECR
    needs: [build, set_env]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Restore cache
        uses: peek-tech/tools-infra-actions/.github/actions/restore@main
        with:
          cache_key: "node_modules"

      - name: Download dist
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: ./dist

      - name: Build Dockerfile
        run: docker build -t 112470036249.dkr.ecr.us-east-1.amazonaws.com/peek-backend-v3-dev:latest -f Dockerfile.api .

      - name: Authenticate on ECR
        run: aws ecr get-login-password | docker login --username AWS --password-stdin 112470036249.dkr.ecr.us-east-1.amazonaws.com

      - name: Docker push
        run: docker push 112470036249.dkr.ecr.us-east-1.amazonaws.com/peek-backend-v3-dev:latest

  deploy_serverless:
    needs: [build, set_env]
    runs-on: 8_32_runner
    container:
      image: ghcr.io/peek-tech/node20:latest
    strategy:
      matrix:
        config_file:
          [
            serverless.yml,
            serverless-dx.yaml,
            serverless-prospects.yaml,
            serverless-vacancy.yaml
          ]
    name: Deploy ${{ matrix.config_file }}

    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Restore cache
        uses: peek-tech/tools-infra-actions/.github/actions/restore@main
        with:
          cache_key: "node_modules"

      - name: Create env file
        run: echo "${{ vars.DOT_ENV }}" > .env

      - name: Install sls
        run: npm i -g serverless@3.x

      - name: Eslint chmod
        run: chmod -R 777 ./node_modules

      - name: Deploy sls
        run: NODE_OPTIONS="--no-experimental-require-module" sls deploy -s dev --config ${{ matrix.config_file }}

  deploy_api:
    name: Deploy API
    needs: [push_image]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Update ECS Service
        run: aws ecs --region us-east-1 update-service --cluster DEV01 --service peek-backend-dev-v3 --force-new-deployment

      - name: Wait deployment
        run: aws ecs wait services-stable --cluster DEV01 --services peek-backend-dev-v3
