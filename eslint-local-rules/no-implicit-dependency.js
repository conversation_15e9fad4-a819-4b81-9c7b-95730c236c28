const fs = require('fs')
const path = require('path')
const ts = require('typescript')
const nodeModule = require('module')

const builtinModules = new Set(
  nodeModule.builtinModules.flatMap((name) =>
    name.startsWith('node:') ? [name, name.slice(5)] : [name, `node:${name}`]
  )
)

let allowedDeps = []
let tsModuleResolver

function loadAllowedDeps(context) {
  if (allowedDeps.length > 0) return

  const cwd = context.getCwd()
  const pkgPath = path.join(cwd, 'package.json')
  if (fs.existsSync(pkgPath)) {
    const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf8'))
    allowedDeps = [
      ...Object.keys(pkg.dependencies || {}),
      ...Object.keys(pkg.devDependencies || {}),
      ...Object.keys(pkg.peerDependencies || {}),
      ...Object.keys(pkg.optionalDependencies || {})
    ]
  }

  const tsconfigPath = ts.findConfigFile(
    cwd,
    ts.sys.fileExists,
    'tsconfig.json'
  )
  if (tsconfigPath) {
    const config = ts.readConfigFile(tsconfigPath, ts.sys.readFile)
    const parsed = ts.parseJsonConfigFileContent(
      config.config,
      ts.sys,
      path.dirname(tsconfigPath)
    )
    tsModuleResolver = (moduleName) => {
      const result = ts.nodeModuleNameResolver(
        moduleName,
        path.join(cwd, '__fake.ts'),
        parsed.options,
        ts.sys
      )
      return result.resolvedModule?.isExternalLibraryImport === false
    }
  } else {
    tsModuleResolver = () => false
  }
}

function isAllowedImport(source) {
  return (
    source.startsWith('.') || // relative import
    builtinModules.has(source) || // Node built-in
    (tsModuleResolver && tsModuleResolver(source)) // tsconfig path
  )
}

module.exports = {
  meta: {
    type: 'problem',
    docs: {
      description: 'Disallow transitive (undeclared) dependencies'
    },
    messages: {
      notExplicit: "'{{name}}' is not listed in package.json dependencies."
    },
    schema: []
  },

  create(context) {
    loadAllowedDeps(context)

    function checkImport(source, node) {
      if (isAllowedImport(source)) return

      const name = source.startsWith('@')
        ? source.split('/').slice(0, 2).join('/')
        : source.split('/')[0]

      if (!allowedDeps.includes(name)) {
        context.report({
          node,
          messageId: 'notExplicit',
          data: { name }
        })
      }
    }

    return {
      ImportDeclaration(node) {
        checkImport(node.source.value, node)
      },
      CallExpression(node) {
        if (
          node.callee.name === 'require' &&
          node.arguments.length &&
          node.arguments[0].type === 'Literal'
        ) {
          checkImport(node.arguments[0].value, node)
        }
      }
    }
  }
}
