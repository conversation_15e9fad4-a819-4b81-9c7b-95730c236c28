import { logDebug } from '@core/log'
import { IdempotentRequestsModel } from '@core/models/idempotentRequests'
import { IdempotentRequest } from '@core/types'
import { NextFunction, Request, Response } from 'express'

// Status code which will be repeated even if the idempotent request already exists, because time itself could be the reason why the second request might need to be repeated
// 404: The resource might not have been available at the time of the first request
// 403: The user might not have access to the resource at the time of the first request
// 500: The request might have failed at the time of the first request for many different reasons
// 502: A third party service might not have been available at the time of the first request
const REPEATABLE_STATUS_CODES = [404, 403, 500, 501, 502, 503]

export const idempotentRequest =
  () => async (req: Request, res: Response, next: NextFunction) => {
    const idempotencyKey = req.headers['x-idempotency-key']

    if (!idempotencyKey) {
      logDebug('Idempotency', 'No idempotency key, skipping...')
      return next()
    }

    if (!['PUT', 'POST', 'PATCH'].includes(req.method)) {
      logDebug('Idempotency', 'PUT, POST or PATCH request, skipping...')
      return next()
    }

    const existing = await IdempotentRequestsModel.findOne({ idempotencyKey })

    // If already completed
    if (existing?.responseStatus) {
      if (REPEATABLE_STATUS_CODES.includes(existing.responseStatus)) {
        return next()
      }

      setHeaders(res, existing.responseHeaders)
      return res
        .status(existing.responseStatus)
        .json(JSON.parse(existing.responseBody))
    }

    // If in progress
    if (existing && !existing.responseStatus) {
      return res.status(425).json({ message: 'Too Early' })
    }

    // Otherwise, store the placeholder
    await IdempotentRequestsModel.create({
      idempotencyKey,
      requestMethod: req.method,
      requestUrl: req.originalUrl,
      requestBody: JSON.stringify(req.body),
      expiresAt: new Date(Date.now() + 60_000) // TTL: 1 minute
    })

    sendHook(res).then(async (body) => {
      await IdempotentRequestsModel.updateOne(
        { idempotencyKey },
        {
          responseStatus: res.statusCode,
          responseBody: body,
          responseHeaders: JSON.stringify(res.getHeaders())
        }
      )
    })

    return next()
  }

// Override express send to resolve the promise with the body that was sent to the client
export function sendHook(res: Response): Promise<any> {
  return new Promise<any>((resolve) => {
    const defaultSend = res.send.bind(res)
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    res.send = (body?: any) => {
      resolve(body)
      defaultSend(body)
    }
  })
}

function setHeaders(res: Response, headers: string) {
  const headersObj = JSON.parse(headers)
  Object.entries(headersObj).forEach(([key, value]) => {
    res.setHeader(key, value as string | number)
  })
}
