import { logDebug } from '@core/log'
import { createPeekInternalClient } from '@core/request/peekInternalApiClient'

const location = 'latch-gateway'

export async function callLatchPost(path: string, payload: any) {
  const client = createPeekInternalClient()
  logDebug(
    location,
    `Calling Latch gateway (POST): latch${path} payload: ${JSON.stringify(
      payload
    )}`
  )
  const response = await client.post(`latch${path}`, payload)
  logDebug(location, `Latch response: ${JSON.stringify(response.data)}`)

  return response.data
}
