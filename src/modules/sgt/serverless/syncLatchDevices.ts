import { logInfo } from '@core/log'
import { connectDb } from '@core/db'
import { syncLatchDevices } from '../services/latch'

export const handler = async (event: any) => {
  logInfo('syncLatchDevices', 'Starting Process', { event })
  await connectDb()

  const { dxSettingId } = event

  if (!dxSettingId) {
    throw new Error('dxSettingId is required')
  }

  try {
    await syncLatchDevices(dxSettingId)
    logInfo('syncLatchDevices', 'Finished process successfully', {
      dxSettingId
    })

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Latch devices synced successfully',
        dxSettingId
      })
    }
  } catch (error) {
    logInfo('syncLatchDevices', 'Error syncing Latch devices', {
      dxSettingId,
      error: error.message
    })

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Error syncing Latch devices',
        error: error.message,
        dxSettingId
      })
    }
  }
}
