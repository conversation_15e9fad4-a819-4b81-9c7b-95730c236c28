import { callLatchPost } from '../gateways/latchGateway'
import { invokeLatchListDoorsLambda } from './latchLambdaClient'
import { LatchDoor } from '../types/latchTypes'
import { findDXSettingById } from '@modules/communities/repositories/dxSetting'
import { AccessDeviceModel } from '../models/accessDevice'
import {
  AccessDeviceManufacturer,
  AccessDeviceType
} from '../types/accessDevice'
import { logInfo } from '@core/log'

const location = 'latch-service'

export const getLatchAccessCode = async (
  deviceId: string,
  startDate: string,
  endDate: string,
  email: string
): Promise<string> => {
  const data = await callLatchPost(`/access`, {
    doorUuid: deviceId,
    startDate,
    endDate,
    email
  })

  return data.accessCode
}

export const getLatchDoors = async (
  buildingUuid: string
): Promise<LatchDoor[]> => {
  const doors = await invokeLatchListDoorsLambda(buildingUuid)

  return doors
}

export const syncLatchDevices = async (dxSettingId: string) => {
  try {
    const dxSetting = await findDXSettingById(dxSettingId)

    if (!dxSetting) {
      throw new Error(`DX Setting with id ${dxSettingId} not found`)
    }

    if (!dxSetting.latch?.buildingUuid) {
      throw new Error('Latch buildingUuid not found in DX setting')
    }

    const buildingUuid = dxSetting.latch.buildingUuid
    const doors = await getLatchDoors(buildingUuid)

    for (const door of doors) {
      const existingDevice = await AccessDeviceModel.findOne({
        identifier: door.uuid,
        'community._id': dxSetting.communityId,
        deletedAt: null
      })

      if (existingDevice) {
        await AccessDeviceModel.findByIdAndUpdate(existingDevice._id, {
          name: door.name
        })
        logInfo(location, `Updated Latch device ${door.uuid}: ${door.name}`)
      } else {
        await AccessDeviceModel.create({
          identifier: door.uuid,
          name: door.name,
          type: AccessDeviceType.Pinpad,
          manufacturer: AccessDeviceManufacturer.Latch,
          isSeamManaged: false,
          model: 'Generic',
          community: {
            _id: dxSetting.communityId
          }
        })
        logInfo(location, `Created Latch device ${door.uuid}: ${door.name}`)
      }
    }

    logInfo(location, `Successfully synced ${doors.length} Latch devices`)
  } catch (error) {
    throw new Error(`Error syncing Latch devices: ${error.message}`)
  }
}
