import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda'
import { LatchDoor } from '../types/latchTypes'
import { logDebug, logInfo } from '@core/log'

const location = 'latch-lambda-client'
const env = process.env.PEEK_ENVIRONMENT || 'dev'

const lambdaClient = new LambdaClient({
  region: process.env.AWS_REGION || 'us-east-1'
})

export const invokeLatchListDoorsLambda = async (
  buildingUuid: string
): Promise<LatchDoor[]> => {
  try {
    logInfo(location, `Invoking Latch Lambda for buildingUuid: ${buildingUuid}`)

    const command = new InvokeCommand({
      FunctionName: `${env}-latch-lambda-list-doors`,
      Payload: JSON.stringify({
        body: JSON.stringify({
          buildingUuid
        })
      }),
      InvocationType: 'RequestResponse'
    })

    const response = await lambdaClient.send(command)

    if (response.StatusCode !== 200) {
      throw new Error(
        `Lambda invocation failed with status: ${response.StatusCode}`
      )
    }

    const payload = JSON.parse(new TextDecoder().decode(response.Payload))

    if (payload.statusCode !== 200) {
      throw new Error(`Lambda returned error: ${payload.body}`)
    }

    const result = JSON.parse(payload.body)
    logDebug(location, `Lambda response: ${JSON.stringify(result)}`)

    return result.doors
  } catch (error) {
    throw new Error(`Failed to invoke Latch Lambda: ${error.message}`)
  }
}
