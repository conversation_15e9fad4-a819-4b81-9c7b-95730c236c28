import { getAclScopeQueryV2 } from '@core/auth'
import { ObjectId } from '@modules/communities/types/id'
import { Query as CrudQuery } from '@core/repositories/CrudRepository'

export const findAclQueryV2 = (query: CrudQuery) => {
  const acl = getAclScopeQueryV2()
  const queryWithAcl = {
    ...query,
    deletedAt: null
  }
  if (acl?.communities?.length > 0) {
    queryWithAcl['communityId'] = {
      $in: acl.communities.map((id) => new ObjectId(id))
    }
  } else if (acl?.organizations?.length > 0) {
    queryWithAcl['organizationId'] = {
      $in: acl.organizations.map((id) => new ObjectId(id))
    }
  }
  return queryWithAcl
}
