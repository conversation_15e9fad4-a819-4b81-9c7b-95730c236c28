import { logDebug, logError, logInfo } from '@core/log'
import { getEnvVariable, sleep } from '@core/util'
import { findExternalLinksByQuery } from '@modules/communities/repositories/externalLink'
import { findSpacesByQuery } from '@modules/communities/repositories/space'
import {
  DXSetting,
  DXSettingServices
} from '@modules/communities/types/dxSetting'
import {
  ExternalLink,
  ExternalLinkCollection,
  ExternalLinkService
} from '@modules/communities/types/externalLink'
import {
  RentCafeV2VirtualTourSent,
  Space
} from '@modules/communities/types/space'
import { getHeaders, loadRentCafeV2Secrets, validateToken } from '.'
import { requestToRentCafe } from '../gateways'
import { findAllDxSettings } from '@modules/communities/repositories/dxSetting'
import {
  updateSpaceById,
  generateSpaceToken
} from '@modules/communities/services/space'
import { RentCafeV2VirtualTourPayload, VirtualTourType } from '../types'

const location = 'rent-cafe-v2-sync-virtual-tours'
let secrets = null

export const syncVirtualTours = async () => {
  secrets = await loadRentCafeV2Secrets()

  const dxSettings = await getDxSettings()
  const externalLinks = await getExternalLinks(dxSettings)
  const spaceIds = externalLinks.map((externalLink) =>
    externalLink.objectId.toString()
  )

  const spaces = await findSpacesByQuery(
    {
      _id: { $in: spaceIds }
    },
    null,
    {
      includeSoftDeleted: true
    }
  )

  logInfo(
    location,
    `Sync RentCafeV2 Virtual Tours - start - ${spaces.length} spaces`
  )

  let floorPlanSpaces: Partial<Space>[] = []

  for (const space of spaces) {
    const dxSetting = dxSettings.find((dxSetting) => {
      return dxSetting.communityId.toString() == space.community._id.toString()
    })
    const externalLink = externalLinks.find((externalLink) => {
      return externalLink.objectId.toString() == space._id.toString()
    })

    floorPlanSpaces = addToFloorPlanSpaces(floorPlanSpaces, space)

    await sendVirtualTour(space, dxSetting, externalLink, VirtualTourType.Unit)
  }

  for (const space of floorPlanSpaces) {
    const dxSetting = dxSettings.find((dxSetting) => {
      return dxSetting.communityId.toString() == space.community._id.toString()
    })
    const externalLink = externalLinks.find((externalLink) => {
      return externalLink.objectId.toString() == space._id.toString()
    })

    await sendVirtualTour(
      space,
      dxSetting,
      externalLink,
      VirtualTourType.FloorPlan
    )
  }

  logInfo(location, 'Sync RentCafeV2 Virtual Tours - finish')
}

const addToFloorPlanSpaces = (
  floorPlanSpaces: Partial<Space>[],
  space: Partial<Space>
) => {
  const existingSpace = floorPlanSpaces.find((floorPlanSpace) => {
    return floorPlanSpace.floorPlan.externalId == space.floorPlan.externalId
  })

  if (!existingSpace) {
    return [...floorPlanSpaces, space]
  }

  if (existingSpace && space.isModelUnit) {
    const floorPlanSpacesWithoutModel = floorPlanSpaces.filter(
      (floorPlanSpace) => {
        return floorPlanSpace.floorPlan.externalId != space.floorPlan.externalId
      }
    )

    return [...floorPlanSpacesWithoutModel, space]
  }

  return floorPlanSpaces
}

const findBestRepresentativeSpaceForFloorPlan = async (
  space: Partial<Space>
): Promise<Partial<Space> | null> => {
  if (!space.floorPlan?.externalId || !space.community?._id) {
    return null
  }

  // Find all spaces with the same floor plan in the same community
  const spacesWithSameFloorPlan = await findSpacesByQuery(
    {
      'floorPlan.externalId': space.floorPlan.externalId,
      'community._id': space.community._id,
      isComplete: true,
      deletedAt: null
    },
    '_id token isModelUnit createdAt',
    {
      includeSoftDeleted: false
    }
  )

  if (!spacesWithSameFloorPlan.length) {
    return null
  }

  // First, try to find a model unit
  const modelUnit = spacesWithSameFloorPlan.find((s) => s.isModelUnit)
  if (modelUnit) {
    return modelUnit
  }

  // If no model unit, return the most recently created space
  const sortedSpaces = spacesWithSameFloorPlan.sort(
    (a, b) =>
      new Date(b.createdAt || 0).getTime() -
      new Date(a.createdAt || 0).getTime()
  )

  return sortedSpaces[0]
}

export const sendVirtualTour = async (
  space: Partial<Space>,
  dxSetting: DXSetting,
  externalLink: ExternalLink,
  virtualTourType: VirtualTourType
) => {
  const baseUrl = getEnvVariable('WEB_VIEWER_BASE_URL')

  let tourToken = space.token

  // For floor plan tours, find the best representative space for this floor plan
  if (virtualTourType === VirtualTourType.FloorPlan) {
    const representativeSpace = await findBestRepresentativeSpaceForFloorPlan(
      space
    )
    if (representativeSpace) {
      tourToken =
        representativeSpace.token ||
        (await generateSpaceToken(representativeSpace._id.toString()))
    }
  }

  const tourLink = `${baseUrl}?token=${tourToken}&embed&pageType=${
    virtualTourType === VirtualTourType.FloorPlan ? 'layout' : 'unit'
  }`

  const hasVirtualTour = space.isComplete || false
  const isDeleted = space.deletedAt != null
  const sentStatus = space.rentCafeV2VirtualTourSent

  let shouldSend = false
  let publish = false

  if (
    hasVirtualTour &&
    isDeleted &&
    sentStatus != RentCafeV2VirtualTourSent.Unpublished
  ) {
    publish = false
    shouldSend = true
  }

  if (
    hasVirtualTour &&
    !isDeleted &&
    sentStatus != RentCafeV2VirtualTourSent.Published
  ) {
    publish = true
    shouldSend = true
  }

  if (!shouldSend) return

  logInfo(location, `Sending Virtual Tour link for space ${space._id}`)
  await sleep(250)

  const payload: RentCafeV2VirtualTourPayload = {
    apiToken: dxSetting.rentCafeV2.apiToken,
    companyCode: dxSetting.rentCafeV2.companyCode,
    propertyCode: dxSetting.rentCafeV2.propertyCode,
    propertyId: dxSetting.rentCafeV2.propertyId,
    floorplanId: space.floorPlan.externalId,
    unitId: externalLink.externalId,
    mediaType: virtualTourType,
    url: tourLink,
    publishToWebsite: publish
  }

  logDebug(location, `Virtual tour payload: ${JSON.stringify(payload)}`)

  const success = await postToRentCafeV2(payload, dxSetting)
  if (success) {
    await updateSpaceById(space._id.toString(), {
      rentCafeV2VirtualTourSent: publish
        ? RentCafeV2VirtualTourSent.Published
        : RentCafeV2VirtualTourSent.Unpublished
    })

    logInfo(location, `Virtual Tour successfully sent for ${space._id}`)
  }
}

export const postToRentCafeV2 = async (payload: any, dxSetting: DXSetting) => {
  await validateToken(secrets.CURRENT_TOKEN)

  const base = dxSetting.rentCafeV2.url ?? secrets.RENT_CAFE_V2_API_URL
  const url = base + '/property/websitemedia'

  const headers = getHeaders()

  try {
    await requestToRentCafe({ url, headers, body: payload })

    return true
  } catch (error) {
    logError(
      location,
      `Error syncing virtual tour for unit ${payload.unitId}: ${error.message}`
    )
    return false
  }
}

const getDxSettings = async () => {
  return findAllDxSettings({
    service: DXSettingServices.RENT_CAFE_V2,
    'rentCafeV2.sendVirtualTours': true
  })
}

const getExternalLinks = async (dxSettings: DXSetting[]) => {
  const communityIds = dxSettings.map((dxSetting) =>
    dxSetting.communityId.toString()
  )

  return findExternalLinksByQuery({
    service: ExternalLinkService.RentCafeV2,
    collectionPath: ExternalLinkCollection.Spaces,
    communityId: { $in: communityIds }
  })
}
