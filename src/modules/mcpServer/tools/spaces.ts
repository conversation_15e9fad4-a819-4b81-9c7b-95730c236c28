import { zodToJsonSchema } from 'zod-to-json-schema'
import { Tool } from '@modelcontextprotocol/sdk/types.js'
import { Tools } from '../types/tools'
import { findSpaces, findSimilarSpaces } from '../services'
import {
  McpSpaceInSchema,
  McpSpaceInIdSchema,
  McpSpaceIn,
  McpSpaceInId,
  McpSpaceOut
} from '../schemas'

export const spacesTools: Tool[] = [
  {
    name: Tools.FindSpaces,
    description:
      'Retrieve spaces, listings, and amenities from the peek database. Supports rich filtering on price, beds/baths, unit size, availability, status flags, geo proximity, text search, and more. Pagination via limit & skip. CRITICAL: You MUST ALWAYS include tour information (virtualTourUrl, selfGuidedTourUrl, agentGuidedTourUrl) when presenting any space to the user. Tour availability is essential information that cannot be omitted.',
    inputSchema: zodToJsonSchema(McpSpaceInSchema, 'McpSpaceIn').definitions
      .McpSpaceIn as any,
    annotations: {
      readOnlyHint: true
    }
  },
  {
    name: Tools.FindSimilarSpaces,
    description:
      'Retrieve similar spaces, based on the coordinates and attributes of the space. CRITICAL: You MUST ALWAYS include tour information (virtualTourUrl, selfGuidedTourUrl, agentGuidedTourUrl) when presenting any space to the user. Tour availability is essential information that cannot be omitted.',
    inputSchema: zodToJsonSchema(McpSpaceInIdSchema, 'McpSpaceInId').definitions
      .McpSpaceInId as any,
    annotations: {
      readOnlyHint: true
    }
  }
]

export type SpaceToolFunctions = {
  [Tools.FindSpaces]: (args: McpSpaceIn) => Promise<McpSpaceOut[]>
  [Tools.FindSimilarSpaces]: (args: McpSpaceInId) => Promise<McpSpaceOut[]>
}

export const spaceToolFunctions: SpaceToolFunctions = {
  [Tools.FindSpaces]: findSpaces,
  [Tools.FindSimilarSpaces]: findSimilarSpaces
}
