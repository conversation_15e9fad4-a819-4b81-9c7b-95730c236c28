import { Tool } from '@modelcontextprotocol/sdk/types.js'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Tools } from '../types/tools'
import {
  McpDaywiseSpaceViewsInSchema,
  McpDaywiseSpaceViewsIn,
  McpViewedSpacesInSchema,
  McpViewedSpacesIn,
  McpCommunitiesAnalyticsInSchema,
  McpCommunitiesAnalyticsIn
} from '../schemas'
import {
  getDaywiseSpaceViews,
  getViewedSpaces,
  getCommunitiesAnalytics
} from '../services/analytics'

export const analyticsTools: Tool[] = [
  {
    name: Tools.DaywiseSpaceViews,
    description:
      'Retrieve day-wise aggregated space view metrics (views and durations) for spaces. Supports filtering by spaceType, communityId, organizationId, prospectId; grouping by spaceId or source; ordering via orderBy and order; and pagination via limit & offset.',
    inputSchema: zodToJsonSchema(
      McpDaywiseSpaceViewsInSchema,
      'McpDaywiseSpaceViewsInSchema'
    ).definitions.McpDaywiseSpaceViewsInSchema as any,
    annotations: {
      readOnlyHint: true
    }
  },
  {
    name: Tools.ViewedSpaces,
    description:
      'Retrieve detailed space view metrics (total, count, avgDuration, items) for spaces. Supports filters by spaceType, communityId, organizationId, prospectId; grouping by spaceId, spaceBedrooms, spaceBathrooms, spaceFloorPlan, or referrer; ordering via orderBy and order; and pagination via limit & offset.',
    inputSchema: zodToJsonSchema(
      McpViewedSpacesInSchema,
      'McpViewedSpacesInSchema'
    ).definitions.McpViewedSpacesInSchema as any,
    annotations: {
      readOnlyHint: true
    }
  },
  {
    name: Tools.GetCommunitiesAnalytics,
    description:
      'Retrieve aggregated analytics for communities (total views, total duration, average duration, and most viewed space). Supports filtering by amount, unit, communityId, and organizationId.',
    inputSchema: zodToJsonSchema(
      McpCommunitiesAnalyticsInSchema,
      'McpCommunitiesAnalyticsInSchema'
    ).definitions.McpCommunitiesAnalyticsInSchema as any,
    annotations: {
      readOnlyHint: true
    }
  }
]

export type AnalyticsToolFunctions = {
  [Tools.DaywiseSpaceViews]: (args: McpDaywiseSpaceViewsIn) => Promise<any>
  [Tools.ViewedSpaces]: (args: McpViewedSpacesIn) => Promise<any>
  [Tools.GetCommunitiesAnalytics]: (
    args: McpCommunitiesAnalyticsIn
  ) => Promise<any>
}

export const analyticsToolFunctions: AnalyticsToolFunctions = {
  [Tools.DaywiseSpaceViews]: getDaywiseSpaceViews,
  [Tools.ViewedSpaces]: getViewedSpaces,
  [Tools.GetCommunitiesAnalytics]: getCommunitiesAnalytics
}
