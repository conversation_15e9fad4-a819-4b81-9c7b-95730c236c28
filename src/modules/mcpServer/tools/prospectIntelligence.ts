import { Tool } from '@modelcontextprotocol/sdk/types.js'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Tools } from '../types/tools'
import {
  McpProspectIntelligenceInSchema,
  McpProspectIntelligenceIn,
  McpProspectActivitiesInSchema,
  McpProspectActivitiesIn,
  McpPiActivityIn,
  McpPiActivityInSchema
} from '../schemas'
import {
  getProspectsIntelligence,
  getPiActivities,
  getPiActivity
} from '../services/prospectIntelligence'

export const prospectIntelligenceTools: Tool[] = [
  {
    name: Tools.GetProspectsIntelligence,
    description:
      'Retrieve prospects intelligence including metrics such as units viewed, view time, and scores. Supports filtering, pagination, and ordering.',
    inputSchema: zodToJsonSchema(
      McpProspectIntelligenceInSchema,
      'McpProspectIntelligenceInSchema'
    ).definitions.McpProspectIntelligenceInSchema as any,
    annotations: {
      readOnlyHint: true
    }
  },
  {
    name: Tools.PiActivities,
    description:
      'Retrieve Prospect-Intelligence activities list; supports spaceType, amount, unit, activity, eventType, offset, limit, groupBy, prospectsMode, orderBy, order, communityId, organizationId.',
    inputSchema: zodToJsonSchema(
      McpProspectActivitiesInSchema,
      'McpProspectActivitiesInSchema'
    ).definitions.McpProspectActivitiesInSchema as any,
    annotations: {
      readOnlyHint: true
    }
  },
  {
    name: Tools.PiActivity,
    description:
      'Retrieve a single Prospect-Intelligence activity; supports spaceType, amount, unit, activity, eventType, offset, limit, groupBy, prospectsMode, orderBy, order, communityId, organizationId.',
    inputSchema: zodToJsonSchema(McpPiActivityInSchema, 'McpPiActivityInSchema')
      .definitions.McpPiActivityInSchema as any,
    annotations: {
      readOnlyHint: true
    }
  }
]

export type ProspectIntelligenceToolFunctions = {
  [Tools.GetProspectsIntelligence]: (
    args: McpProspectIntelligenceIn
  ) => Promise<{ data: any[]; totalCount: number }>
  [Tools.PiActivities]: (args: McpProspectActivitiesIn) => Promise<any>
  [Tools.PiActivity]: (args: McpPiActivityIn) => Promise<any>
}

export const prospectIntelligenceToolFunctions: ProspectIntelligenceToolFunctions =
  {
    [Tools.GetProspectsIntelligence]: getProspectsIntelligence,
    [Tools.PiActivities]: getPiActivities,
    [Tools.PiActivity]: (args: McpPiActivityIn) => getPiActivity(args)
  }
