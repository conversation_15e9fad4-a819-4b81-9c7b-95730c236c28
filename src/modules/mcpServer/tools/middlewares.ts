import { PeekRequest } from '@api/interfaces'
import { Tools } from '../types/tools'
import type { NextFunction, Request, Response } from 'express'

export type MiddlewareFn<
  Req extends Request = PeekRequest,
  Res extends Response = Response,
  Next extends NextFunction = NextFunction
> = (req: Req, res: Res, next: Next) => Promise<void> | void

export const toolMiddlewares: Partial<Record<Tools, MiddlewareFn[]>> = {}

export function registerToolMiddlewares(
  tool: Tools,
  middlewares: MiddlewareFn[]
) {
  toolMiddlewares[tool] = middlewares
}
