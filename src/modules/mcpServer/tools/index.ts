import { Tool } from '@modelcontextprotocol/sdk/types.js'

import { spacesTools, spaceToolFunctions, SpaceToolFunctions } from './spaces'

import {
  communitiesTools,
  communityToolFunctions,
  CommunityToolFunctions
} from './communities'

import {
  prospectsTools,
  prospectToolFunctions,
  ProspectToolFunctions
} from './prospects'

import {
  prospectIntelligenceTools,
  prospectIntelligenceToolFunctions,
  ProspectIntelligenceToolFunctions
} from './prospectIntelligence'

import {
  prospectEnhancementTools,
  prospectEnhancementToolFunctions,
  ProspectEnhancementToolFunctions
} from './prospectEnhancement'

const tools: Tool[] = [
  ...spacesTools,
  ...communitiesTools,
  ...prospectsTools,
  ...prospectIntelligenceTools,
  ...prospectEnhancementTools
]

interface ToolFunctionSignatures
  extends SpaceToolFunctions,
    CommunityToolFunctions,
    ProspectToolFunctions,
    ProspectIntelligenceToolFunctions,
    ProspectEnhancementToolFunctions {}

export const toolFunctions: ToolFunctionSignatures = {
  ...spaceToolFunctions,
  ...communityToolFunctions,
  ...prospectToolFunctions,
  ...prospectIntelligenceToolFunctions,
  ...prospectEnhancementToolFunctions
}

export default tools

export { toolMiddlewares, registerToolMiddlewares } from './middlewares'
export type { MiddlewareFn } from './middlewares'
