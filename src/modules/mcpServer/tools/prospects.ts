import { Tool } from '@modelcontextprotocol/sdk/types.js'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Tools } from '../types/tools'
import { McpProspectInSchema, McpProspectIn } from '../schemas'
import { createProspect } from '../services'

export const prospectsTools: Tool[] = [
  {
    name: Tools.CreateProspect,
    description:
      'Send a message to the agent in case the prospect is interested in the space. The agent will receive the message and can respond to the prospect.',
    inputSchema: zodToJsonSchema(McpProspectInSchema, 'McpProspectIn')
      .definitions.McpProspectIn as any
  }
]

export type ProspectToolFunctions = {
  [Tools.CreateProspect]: (args: McpProspectIn) => Promise<any>
}

export const prospectToolFunctions: ProspectToolFunctions = {
  [Tools.CreateProspect]: createProspect
}
