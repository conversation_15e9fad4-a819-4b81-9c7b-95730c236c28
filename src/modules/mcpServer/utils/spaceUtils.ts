import { McpSpaceOut } from '../schemas/mcpSpaceOut'
import {
  agentGuidedTourUrl,
  selfGuidedTourUrl,
  virtualTourUrl
} from '@modules/communities/helpers/tours'
import { SpaceToMcpOutInput } from '../types/space'
import { CommunityTours } from '@modules/communities/types/community'

export const spaceToMcpSpaceOut = (space: SpaceToMcpOutInput): McpSpaceOut => {
  const { communityFull, ...spaceWithoutCommunityFull } = space
  return {
    ...spaceWithoutCommunityFull,
    community: {
      _id: communityFull._id.toString(),
      name: communityFull.name
    },
    _id: space._id.toString(),
    virtualTourUrl: virtualTourUrl(space.token),
    ...(communityFull.tours[CommunityTours.SGT]?.isAvailable && {
      selfGuidedTourUrl: selfGuidedTourUrl(communityFull._id.toString())
    }),
    ...(communityFull.tours[CommunityTours.AGT]?.isAvailable && {
      agentGuidedTourUrl: agentGuidedTourUrl(communityFull._id.toString())
    })
  }
}

export const spaceMcpOutProjection = {
  _id: 1,
  token: 1,
  community: {
    _id: 1,
    name: 1,
    address: 1
  },
  building: {
    _id: 1,
    name: 1,
    address: 1
  },
  address: 1,
  unit: 1,
  bedrooms: 1,
  bathrooms: 1,
  unitSize: 1,
  amenities: 1,
  description: 1,
  floorPlan: 1,
  displayPrice: 1,
  availabilityStatus: 1,
  availableDate: 1,
  readyToRentDate: 1,
  category: 1,
  spaceCategory: 1,
  spaceFunction: 1,
  spaceDetail: 1
}
