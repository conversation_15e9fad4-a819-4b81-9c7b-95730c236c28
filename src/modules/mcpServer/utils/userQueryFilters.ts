import { getAclScopeQueryV2 } from '@core/auth'
import { Types } from 'mongoose'

export const applyAclFilters = (params: any): any => {
  const aclScope = getAclScopeQueryV2()

  if (!aclScope) {
    return params
  }

  const { communities, organizations } = aclScope
  const filteredParams = { ...params }

  if (communities && communities.length > 0) {
    if (!filteredParams.communityId) {
      filteredParams.communityId = communities.map(
        (id) => new Types.ObjectId(id)
      )
    } else {
      const userCommunityIds = new Set(communities.map((id) => id.toString()))
      const requestedCommunityIds = Array.isArray(filteredParams.communityId)
        ? filteredParams.communityId
        : [filteredParams.communityId]

      filteredParams.communityId = requestedCommunityIds
        .filter((id) => userCommunityIds.has(id.toString()))
        .map((id) => new Types.ObjectId(id))
    }
  }

  if (organizations && organizations.length > 0) {
    if (!filteredParams.organizationId) {
      filteredParams.organizationId = organizations.map(
        (id) => new Types.ObjectId(id)
      )
    } else {
      const userOrganizationIds = new Set(
        organizations.map((id) => id.toString())
      )
      const requestedOrganizationIds = Array.isArray(
        filteredParams.organizationId
      )
        ? filteredParams.organizationId
        : [filteredParams.organizationId]

      filteredParams.organizationId = requestedOrganizationIds
        .filter((id) => userOrganizationIds.has(id.toString()))
        .map((id) => new Types.ObjectId(id))
    }
  }

  return filteredParams
}
