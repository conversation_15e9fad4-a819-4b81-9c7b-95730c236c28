import { Space } from '@modules/communities/types/space'
import { McpSpaceIn } from '../schemas/mcpSpaceIn'
import { Types } from 'mongoose'
import { McpCommunityIn } from '../schemas/mcpCommunityIn'
import { Community } from '@modules/communities/types/community'

const ADDRESS_FIELDS = [
  'street',
  'street2',
  'city',
  'state',
  'zip',
  'addressString'
]

function applyAddressCondition(
  queryObj: Record<string, any>,
  addressInput: string | undefined,
  prefix: string
) {
  if (!addressInput) return

  queryObj.$or = ADDRESS_FIELDS.map((field) => ({
    [`${prefix}${field}`]: {
      $regex: escapeRegex(addressInput),
      $options: 'i'
    }
  }))
}

/**
 * Transforms a validated `McpSpaceIn` object into a MongoDB query compatible
 * with the `space` collection schema.
 */
export function buildSpaceQuery(input: McpSpaceIn, query?: Partial<Space>) {
  const q: Record<string, any> = {}

  if (input._id) q._id = new Types.ObjectId(input._id)
  if (input.token) q.token = input.token

  if (input.communityId)
    q['community._id'] = new Types.ObjectId(input.communityId)
  if (input.buildingId) q['building._id'] = new Types.ObjectId(input.buildingId)
  if (input.communityName)
    q['community.name'] = {
      $regex: `^${escapeRegex(input.communityName)}$`,
      $options: 'i'
    }
  if (input.buildingName)
    q['building.name'] = {
      $regex: `^${escapeRegex(input.buildingName)}$`,
      $options: 'i'
    }
  if (input.buildingAlternativeName)
    q['building.alternativeName'] = {
      $regex: `^${escapeRegex(input.buildingAlternativeName)}$`,
      $options: 'i'
    }

  if (input.unit) q.unit = input.unit
  if (input.bedrooms) q.bedrooms = input.bedrooms
  if (input.bathrooms) q.bathrooms = input.bathrooms
  if (input.unitSize) q.unitSize = input.unitSize

  addRange(q, 'bedrooms', input.minBedrooms, input.maxBedrooms)
  addRange(q, 'bathrooms', input.minBathrooms, input.maxBathrooms)
  addRange(q, 'unitSize', input.minUnitSize, input.maxUnitSize)
  addRange(q, 'displayPrice', input.minDisplayPrice, input.maxDisplayPrice)

  addDateRange(
    q,
    'availableDate',
    input.availableDateFrom,
    input.availableDateTo
  )
  addDateRange(
    q,
    'readyToRentDate',
    input.readyToRentDateFrom,
    input.readyToRentDateTo
  )

  if (input.unitAmenitiesAll) q.amenities = { $all: input.unitAmenitiesAll }
  else if (input.unitAmenitiesAny) q.amenities = { $in: input.unitAmenitiesAny }

  if (input.floorPlanName)
    q['floorPlan.name'] = {
      $regex: `^${escapeRegex(input.floorPlanName)}$`,
      $options: 'i'
    }

  applyAddressCondition(q, input.address, 'building.address.')

  if (query) {
    Object.assign(q, query)
  }

  return q
}

export function buildCommunityQuery(
  input: McpCommunityIn,
  query?: Partial<Community>
) {
  const q: Record<string, any> = {}

  if (input._id) q._id = new Types.ObjectId(input._id)
  if (input.name) q.name = { $regex: escapeRegex(input.name), $options: 'i' }

  applyAddressCondition(q, input.address, 'address.')

  if (query) {
    Object.assign(q, query)
  }

  return q
}

function addRange(obj: any, field: string, min?: number, max?: number) {
  if (min !== undefined || max !== undefined) {
    obj[field] = {}
    if (min !== undefined) obj[field].$gte = min
    if (max !== undefined) obj[field].$lte = max
  }
}

function addDateRange(obj: any, field: string, from?: string, to?: string) {
  if (from || to) {
    obj[field] = {}
    if (from) obj[field].$gte = new Date(from)
    if (to) obj[field].$lte = new Date(to)
  }
}

function escapeRegex(str: string) {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}
