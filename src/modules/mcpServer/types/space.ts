import { Community } from '@modules/communities/types/community'
import { Space } from '@modules/communities/types/space'

export type SpaceToMcpOutInput = Pick<
  Space,
  | '_id'
  | 'token'
  | 'community'
  | 'building'
  | 'address'
  | 'unit'
  | 'bedrooms'
  | 'bathrooms'
  | 'unitSize'
  | 'amenities'
  | 'description'
  | 'floorPlan'
  | 'displayPrice'
  | 'availabilityStatus'
  | 'availableDate'
  | 'readyToRentDate'
  | 'category'
  | 'spaceCategory'
  | 'spaceFunction'
  | 'spaceDetail'
> & {
  communityFull: Pick<Community, '_id' | 'name' | 'tours'>
}
