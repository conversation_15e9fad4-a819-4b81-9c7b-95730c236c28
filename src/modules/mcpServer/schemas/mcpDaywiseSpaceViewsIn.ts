import { z } from 'zod'
import { ObjectIdSchema } from './common'

const SpaceTypeEnum = z.enum(['unit', 'amenity'])
const TimeUnitEnum = z.enum(['minute', 'hour', 'day', 'week', 'month'])
const GroupByEnum = z.enum(['spaceId', 'source'])

export const McpDaywiseSpaceViewsInSchema = z.object({
  spaceType: SpaceTypeEnum.optional().describe('Space type'),
  amount: z
    .number()
    .int()
    .min(1)
    .max(90)
    .default(10)
    .optional()
    .describe('Amount'),
  unit: TimeUnitEnum.optional().describe('Unit'),
  communityId: z
    .union([ObjectIdSchema, z.array(ObjectIdSchema)])
    .optional()
    .describe('Community ID'),
  organizationId: z
    .union([ObjectIdSchema, z.array(ObjectIdSchema)])
    .optional()
    .describe('Organization ID'),
  prospectId: ObjectIdSchema.optional().describe('Prospect ID'),
  groupBy: z
    .union([GroupByEnum, z.array(GroupByEnum)])
    .optional()
    .describe('Group by'),
  date: z.string().optional().describe('Date'),
  distinct: z.boolean().optional().describe('Distinct'),
  offset: z
    .number()
    .int()
    .min(0)
    .max(100)
    .default(0)
    .optional()
    .describe('Offset'),
  limit: z
    .number()
    .int()
    .min(1)
    .max(100)
    .default(10)
    .optional()
    .describe('Limit'),
  orderBy: z.enum(['duration', 'views']).optional().describe('Order by'),
  order: z.enum(['asc', 'desc']).optional().describe('Order')
})

export type McpDaywiseSpaceViewsIn = z.infer<
  typeof McpDaywiseSpaceViewsInSchema
>
