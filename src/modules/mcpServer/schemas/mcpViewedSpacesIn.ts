import { z } from 'zod'
import { ObjectIdSchema } from './common'

const SpaceTypeEnum = z.enum(['unit', 'amenity'])
const TimeUnitEnum = z.enum(['minute', 'hour', 'day', 'week', 'month'])
const GroupByEnum = z.enum([
  'spaceId',
  'spaceBedrooms',
  'spaceBathrooms',
  'spaceFloorPlan',
  'referrer'
])

export const McpViewedSpacesInSchema = z.object({
  spaceType: SpaceTypeEnum.optional().describe('Space type'),
  amount: z
    .number()
    .int()
    .min(1)
    .max(90)
    .default(10)
    .optional()
    .describe('Amount'),
  unit: TimeUnitEnum.optional().describe('Unit'),
  communityId: z
    .union([ObjectIdSchema, z.array(ObjectIdSchema)])
    .optional()
    .describe('Community ID'),
  organizationId: z
    .union([ObjectIdSchema, z.array(ObjectIdSchema)])
    .optional()
    .describe('Organization ID'),
  prospectId: ObjectIdSchema.optional().describe('Prospect ID'),
  groupBy: z
    .union([GroupByEnum, z.array(GroupByEnum)])
    .optional()
    .describe('Group by fields'),
  date: z.string().optional().describe('Start date for filtering'),
  offset: z
    .number()
    .int()
    .min(0)
    .max(100)
    .default(0)
    .optional()
    .describe('Offset for pagination'),
  limit: z
    .number()
    .int()
    .min(1)
    .max(100)
    .default(5)
    .optional()
    .describe('Limit for pagination'),
  orderBy: z.enum(['views', 'duration']).optional().describe('Order by field'),
  order: z.enum(['asc', 'desc']).optional().describe('Order direction')
})

export type McpViewedSpacesIn = z.infer<typeof McpViewedSpacesInSchema>
