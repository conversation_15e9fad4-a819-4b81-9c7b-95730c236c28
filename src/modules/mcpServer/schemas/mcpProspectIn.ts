import { z } from 'zod'

export const McpProspectInSchema = z.object({
  communityId: z.string().describe('Community ID'),
  email: z.string().email().describe('Email'),
  phone: z.string().describe('Phone number'),
  firstName: z.string().describe('First name'),
  lastName: z.string().describe('Last name'),
  message: z.string().describe('Message, optional').optional(),
  spaceId: z.string().describe('Space ID, optional').optional()
})

export type McpProspectIn = z.infer<typeof McpProspectInSchema>
