import { z } from 'zod'
import { ObjectIdSchema } from './common'
import { McpAmenityOutSchema } from './mcpAmenityOut'

export const McpCommunityOutSchema = z.object({
  _id: ObjectIdSchema.describe('Exact Community _id to fetch').optional(),
  name: z
    .string()
    .describe('Exact Community name to fetch (case insensitive)')
    .optional(),
  amenities: z.array(z.string()).optional().describe('Amenity names'),
  amenitiesSpaces: z
    .array(McpAmenityOutSchema)
    .optional()
    .describe('Amenities with details'),
  address: z.string().optional(),
  welcomeSiteUrl: z
    .string()
    .optional()
    .describe('The URL of the community welcome site'),
  pointsOfInterest: z
    .array(z.string())
    .optional()
    .describe('Points of interest names')
})

export type McpCommunityOut = z.infer<typeof McpCommunityOutSchema>
