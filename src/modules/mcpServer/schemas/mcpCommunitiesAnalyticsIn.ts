import { z } from 'zod'
import { ObjectIdSchema } from './common'

const TimeUnitEnum = z.enum(['minute', 'hour', 'day', 'week', 'month'])

export const McpCommunitiesAnalyticsInSchema = z.object({
  amount: z
    .number()
    .int()
    .min(1)
    .max(90)
    .default(10)
    .optional()
    .describe('Amount'),
  unit: TimeUnitEnum.optional().describe('Time unit'),
  communityId: z
    .union([ObjectIdSchema, z.array(ObjectIdSchema)])
    .optional()
    .describe('Community ID(s)'),
  organizationId: z
    .union([ObjectIdSchema, z.array(ObjectIdSchema)])
    .optional()
    .describe('Organization ID(s)')
})

export type McpCommunitiesAnalyticsIn = z.infer<
  typeof McpCommunitiesAnalyticsInSchema
>
