import { z } from 'zod'
import { Community } from '@modules/communities/types/community'
import { Building } from '@modules/communities/types/building'
import {
  FloorPlan,
  ImageUpload,
  RentPrice,
  RoomPlanDimension
} from '@modules/communities/types/space'
import { Address } from '@modules/communities/types/address'

export const CommunitySchema: z.ZodType<Partial<Community>> = z.object({
  _id: z.string(),
  name: z.string()
})

export const RoomPlanDimensionSchema = z.object({
  height: z.number(),
  width: z.number(),
  length: z.number()
}) as z.ZodType<RoomPlanDimension>

export const FloorPlanSchema: z.ZodType<Partial<FloorPlan>> = z.object({
  name: z.string(),
  roomPlanFile: z.string().optional(),
  roomPlanDimension: RoomPlanDimensionSchema.optional(),
  url: z.string().optional()
})

export const ImageUploadSchema: z.ZodType<Partial<ImageUpload>> = z.object({
  url: z.string().optional(),
  urlChangedAt: z.date().optional()
})

export const RentPriceSchema: z.ZodType<Partial<RentPrice>> = z.object({
  termInMonths: z.number(),
  price: z.number(),
  enabled: z.boolean()
})

export const AddressSchema = z.object({
  street: z.string(),
  street2: z.string().optional(),
  city: z.string(),
  state: z.string(),
  postalCode: z.string(),
  number: z.number().optional(),
  country: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  neighborhood: z.string().optional(),
  district: z.string().optional(),
  addressString: z.string().optional(),
  placeId: z.string().optional(),
  location: z
    .object({
      type: z.string(),
      coordinates: z.array(z.number())
    })
    .optional()
}) as z.ZodType<Address>

export const BuildingSchema: z.ZodType<Partial<Building>> = z.object({
  _id: z.string(),
  name: z.string().optional(),
  alternativeName: z.string().optional(),
  address: AddressSchema
})

export const PaginationSchema = z.object({
  skip: z.number().optional().default(0),
  limit: z.number().optional().default(10)
})

export const ObjectIdSchema = z
  .string()
  .regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format')
