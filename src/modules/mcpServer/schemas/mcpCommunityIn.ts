import { z } from 'zod'
import { ObjectIdSchema, PaginationSchema } from './common'

export const McpCommunityInSchema = z
  .object({
    _id: ObjectIdSchema.describe('Exact Community _id to fetch').optional(),
    name: z
      .string()
      .describe('Exact Community name to fetch (case insensitive)')
      .optional(),
    amenities: z
      .array(z.string())
      .describe(
        'Filter by amenity names (case insensitive) to return communities that have at least one amenity with the given name'
      )
      .optional(),
    address: z
      .string()
      .describe('Match communities whose address looks like this')
      .optional()
  })
  .merge(PaginationSchema)

export type McpCommunityIn = z.infer<typeof McpCommunityInSchema>
