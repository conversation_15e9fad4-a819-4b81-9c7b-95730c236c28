import { z } from 'zod'
import { ObjectIdSchema } from './common'

const SpaceTypeEnum = z.enum(['unit', 'amenity'])

const TimeUnitEnum = z.enum(['minute', 'hour', 'day', 'week', 'month'])

const ActivityEnum = z.enum([
  'Inquiry',
  'SGT_booked',
  'SGT_cancelled',
  'SGT_started',
  'SGT_rescheduled',
  'Welcome Site Inquiry',
  'Schedule Tour',
  'Self Guided Tour Inquiry',
  'web-viewer',
  'casa_email_start',
  'casa_email_in',
  'casa_email_out',
  'casa_email_handoff',
  'casa_chat_session',
  'casa_chat_in',
  'casa_chat_out',
  'casa_chat_opened'
])

const EventTypeEnum = z.enum([
  'inquiry',
  'sgt',
  'virtual-tour',
  'appointment',
  'casa'
])

const GroupByEnum = z.enum(['prospect'])

const ProspectsModeEnum = z.enum(['all_prospects', 'new_prospects'])

export const McpProspectActivitiesInSchema = z.object({
  spaceType: SpaceTypeEnum.optional().describe('Space type filter'),
  amount: z
    .number()
    .int()
    .min(1)
    .max(90)
    .default(10)
    .optional()
    .describe('Rolling time window amount'),
  unit: TimeUnitEnum.optional().describe('Rolling time window unit'),

  communityId: z
    .array(ObjectIdSchema)
    .min(1, { message: 'At least one communityId must be provided' })
    .describe('Filter to prospects belonging to any of these community IDs'),
  organizationId: z
    .array(ObjectIdSchema)
    .optional()
    .describe('Filter to prospects belonging to any of these organization IDs'),

  groupBy: GroupByEnum.optional().describe('Grouping dimension'),
  prospectsMode: ProspectsModeEnum.default('all_prospects').optional(),

  offset: z
    .number()
    .int()
    .min(0)
    .max(100)
    .default(0)
    .optional()
    .describe('Pagination offset'),
  limit: z
    .number()
    .int()
    .min(1)
    .max(100)
    .default(10)
    .optional()
    .describe('Pagination limit'),

  orderBy: z.string().optional().describe('Sort by field'),
  order: z.enum(['asc', 'desc']).optional().describe('Sort direction')
})

export type McpProspectActivitiesIn = z.infer<
  typeof McpProspectActivitiesInSchema
>
