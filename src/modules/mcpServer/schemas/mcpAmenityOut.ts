import { z } from 'zod'
import { ObjectIdSchema } from './common'

export const McpAmenityOutSchema = z.object({
  _id: ObjectIdSchema,
  name: z.string(),
  spaceCategory: z.string().optional(),
  spaceFunction: z.string().optional(),
  spaceDetail: z.string().optional(),
  virtualTourUrl: z.string().optional(),
  selfGuidedTourUrl: z.string().optional(),
  agentGuidedTourUrl: z.string().optional()
})

export type McpAmenityOut = z.infer<typeof McpAmenityOutSchema>
