import { z } from 'zod'
import { ObjectIdSchema } from './common'

export const McpProspectIntelligenceInSchema = z.object({
  offset: z
    .number()
    .int()
    .min(0)
    .max(100)
    .default(0)
    .describe(
      'Number of records to skip before the first result (pagination offset)'
    ),
  limit: z
    .number()
    .int()
    .min(1)
    .max(100)
    .default(10)
    .describe('Maximum number of records to return (page size)'),
  search: z
    .string()
    .min(3)
    .max(100)
    .optional()
    .describe(
      'Free-text search across prospect name, email, phone and community / organization / space / room names'
    ),
  amount: z
    .number()
    .int()
    .min(1)
    .max(90)
    .default(2)
    .optional()
    .describe(
      "How many units back from now to include in the time window (e.g. 30 + unit='day' → last 30 days)"
    ),
  unit: z
    .enum(['minute', 'hour', 'day', 'week', 'month'])
    .default('day')
    .optional()
    .describe('Time unit used with amount to define the rolling window'),
  orderBy: z
    .enum([
      'firstName',
      'lastName',
      'email',
      'phone',
      'unitsViewed',
      'viewTime',
      'piScore',
      'piStatus',
      'lastActivity'
    ])
    .optional()
    .describe('Field used to sort the final result set'),
  order: z
    .enum(['asc', 'desc'])
    .optional()
    .describe("Sort direction for 'orderBy' (ascending or descending)"),
  communityId: z
    .array(ObjectIdSchema)
    .min(1, { message: 'At least one communityId must be provided' })
    .describe('Filter to prospects belonging to any of these community IDs'),
  organizationId: z
    .array(ObjectIdSchema)
    .optional()
    .describe('Filter to prospects belonging to any of these organization IDs')
})

export type McpProspectIntelligenceIn = z.infer<
  typeof McpProspectIntelligenceInSchema
>
