import { z } from 'zod'
import { SpaceType } from '@modules/communities/types/spaceType'
import {
  CommunitySchema,
  BuildingSchema,
  FloorPlanSchema,
  AddressSchema
} from './common'

export const McpSpaceOutSchema = z.object({
  _id: z.string(),
  token: z.string().optional(),
  community: CommunitySchema,
  building: BuildingSchema.optional(),
  address: AddressSchema,
  type: z.nativeEnum(SpaceType),
  unit: z.string().optional(),
  bedrooms: z.number().optional(),
  bathrooms: z.number().optional(),
  unitSize: z.number().optional(),
  amenities: z.array(z.string()).optional(),
  description: z.string().optional(),
  floorPlan: FloorPlanSchema.optional(),
  displayPrice: z.number().optional(),
  availabilityStatus: z.string().optional(),
  availableDate: z.date().optional(),
  readyToRentDate: z.date().optional(),
  category: z.string().optional(),
  spaceCategory: z.string().optional(),
  spaceFunction: z.string().optional(),
  spaceDetail: z.string().optional(),
  virtualTourUrl: z.string().optional(),
  selfGuidedTourUrl: z.string().optional(),
  agentGuidedTourUrl: z.string().optional()
})

export type McpSpaceOut = z.infer<typeof McpSpaceOutSchema>
