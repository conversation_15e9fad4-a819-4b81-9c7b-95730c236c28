import { z } from 'zod'
import { ObjectIdSchema, PaginationSchema } from './common'

export const McpSpaceInIdSchema = z.object({
  _id: ObjectIdSchema.describe('Exact Space _id to fetch')
})

export const McpSpaceInSchema = z
  .object({
    _id: ObjectIdSchema.describe('Exact Space _id to fetch').optional(),

    token: z
      .string()
      .describe('Public share token; returns exactly one matching space')
      .optional(),

    communityId: z.string().describe('Filter by community _id').optional(),
    buildingId: z.string().describe('Filter by building _id').optional(),

    communityName: z
      .string()
      .describe(
        'Match spaces whose community name equals (case-insensitive) this'
      )
      .optional(),

    buildingName: z
      .string()
      .describe(
        'Match spaces whose building name equals (case-insensitive) this'
      )
      .optional(),
    buildingAlternativeName: z
      .string()
      .describe('Match spaces whose building alternativeName equals this')
      .optional(),

    unit: z
      .string()
      .describe('Exact unit identifier (e.g. "Apt 12B")')
      .optional(),

    bedrooms: z.number().describe('Exact bedrooms').optional(),
    minBedrooms: z.number().describe('Minimum bedrooms').optional(),
    maxBedrooms: z.number().describe('Maximum bedrooms').optional(),

    bathrooms: z.number().describe('Exact bathrooms').optional(),
    minBathrooms: z.number().describe('Minimum bathrooms').optional(),
    maxBathrooms: z.number().describe('Maximum bathrooms').optional(),

    unitSize: z
      .number()
      .describe('Exact unit size (sq ft or m², depending on locale)')
      .optional(),
    minUnitSize: z.number().describe('Minimum unit size').optional(),
    maxUnitSize: z.number().describe('Maximum unit size').optional(),

    minDisplayPrice: z
      .number()
      .describe('Lower bound for displayPrice (currency in dolars)')
      .optional(),
    maxDisplayPrice: z
      .number()
      .describe('Upper bound for displayPrice (currency in dolars)')
      .optional(),

    availableDateFrom: z
      .string()
      .describe('ISO date – spaces available on/after this date')
      .optional(),
    availableDateTo: z
      .string()
      .describe('ISO date – spaces available on/before this date')
      .optional(),
    readyToRentDateFrom: z
      .string()
      .describe('ISO date – ready-to-rent on/after this date')
      .optional(),
    readyToRentDateTo: z
      .string()
      .describe('ISO date – ready-to-rent on/before this date')
      .optional(),

    unitAmenitiesAll: z
      .array(z.string())
      .nonempty()
      .describe(
        'Require the space to have ALL of these unit amenities: (e.g: dishwasher, washer, dryer)'
      )
      .optional(),

    unitAmenitiesAny: z
      .array(z.string())
      .nonempty()
      .describe(
        'Require the space to have ANY of these unit amenities: (e.g: dishwasher, washer, dryer)'
      )
      .optional(),

    floorPlanName: z
      .string()
      .describe('Match spaces whose floor-plan name equals this')
      .optional(),
    address: z
      .string()
      .describe('Match spaces whose address looks like this')
      .optional()
  })
  .merge(PaginationSchema)

export type McpSpaceIn = z.infer<typeof McpSpaceInSchema>
export type McpSpaceInId = z.infer<typeof McpSpaceInIdSchema>
