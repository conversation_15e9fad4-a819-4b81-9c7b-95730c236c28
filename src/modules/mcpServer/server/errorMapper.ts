import { McpError, ErrorCode } from '@modelcontextprotocol/sdk/types.js'
import { logError } from '@core/log'

/**
 * Translate Peek domain or HTTP-style exceptions into MCP-compliant errors.
 * Falls back to INTERNAL_ERROR while logging unexpected errors.
 */
export function toMcpError(err: any): McpError {
  if (err instanceof McpError) return err

  const status: number | undefined = err?.status ?? err?.statusCode

  switch (status) {
    case 400:
    case 422:
      return new McpError(ErrorCode.InvalidParams, err.message)
    case 401:
    case 403:
    case 409:
    case 429:
      return new McpError(ErrorCode.InvalidRequest, err.message)
    case 404:
      return new McpError(ErrorCode.MethodNotFound, err.message)
    default:
      return new McpError(
        ErrorCode.InternalError,
        err.message ?? 'Internal server error'
      )
  }
}
