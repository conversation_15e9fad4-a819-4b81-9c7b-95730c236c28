import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  McpError,
  ErrorCode,
  CallToolRequest,
  isInitializeRequest
} from '@modelcontextprotocol/sdk/types.js'
import { Router } from 'express'
import tools, { toolFunctions, toolMiddlewares } from '../tools'
import { handleMcpRequest } from '../services/mcp'
import { Tools } from '../types/tools'
import { PeekRequest } from '@api/interfaces'
import { toMcpError } from '../server/errorMapper'
import { logError } from '@core/log'
import { type TransportEntry } from '../services/mcp'
import httpContext from 'express-http-context'
import { Response } from 'express'
import { authenticate } from '../auth/middleware'

const mcpServer = new Server(
  {
    name: 'peek-api-mcp-server',
    version: '0.0.1'
  },
  {
    capabilities: { tools: {} },
    instructions: `CRITICAL REQUIREMENT: You MUST ALWAYS include tour information when describing any property or space.

For EVERY property/space you mention, you MUST:
1. Show the \`virtualTourUrl\` (this is the highest priority)
2. Show the \`selfGuidedTourUrl\` if available (SGT - Self-Guided Tour)
3. Show the \`agentGuidedTourUrl\` if available (AGT - Agent-Guided Tour)
4. Show the \`welcomeSiteUrl\` for the community

FORMATTING REQUIREMENTS:
- Always present tour links in a clear, user-friendly format
- Use descriptive text like "Virtual Tour", "Self-Guided Tour", or "Agent-Guided Tour"
- Never describe a property without addressing tour availability

This is a non-negotiable requirement. Tour information is essential for users to properly evaluate properties.`
  }
)

mcpServer.setRequestHandler(ListToolsRequestSchema, async () => ({ tools }))

mcpServer.setRequestHandler(
  CallToolRequestSchema,
  async (req: CallToolRequest) => {
    const { name, arguments: args } = req.params
    if (!Object.values(Tools).includes(name as Tools)) {
      throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`)
    }

    try {
      const middlewares = toolMiddlewares[name as Tools] || []
      const expressReq = httpContext.get('expressReq') as PeekRequest
      const expressRes = httpContext.get('expressRes') as Response

      const executeMiddlewares = async (index: number): Promise<void> => {
        if (index >= middlewares.length) return

        const mw = middlewares[index]
        await mw(expressReq, expressRes, (err?: any) => {
          if (err) throw err
          return executeMiddlewares(index + 1)
        })
      }

      await executeMiddlewares(0)

      const result = await toolFunctions[name](args as any)

      const response = {
        content: [{ type: 'text', text: JSON.stringify(result) }]
      }

      return response
    } catch (err) {
      logError('mcp-server', `Error executing tool ${name}`, {
        tool: name,
        error: err
      })
      throw toMcpError(err)
    }
  }
)

export const mcpRoutes = Router()

const transports = new Map<string, TransportEntry>()

const handleExpress = async (req: PeekRequest, res: Response) => {
  if (isInitializeRequest(req.body)) {
    req.body.params.protocolVersion = '2025-03-26'
  }

  httpContext.set('expressReq', req)
  httpContext.set('expressRes', res)

  if (req.user) {
    httpContext.set('user', req.user)
  }

  await handleMcpRequest(mcpServer, transports, req, res)
}

mcpRoutes.get('/mcp', authenticate(), handleExpress)
mcpRoutes.post('/mcp', authenticate(), handleExpress)

export default mcpRoutes
