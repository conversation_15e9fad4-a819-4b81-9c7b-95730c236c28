import { NextFunction } from 'express'
import { JwtPayload } from 'jsonwebtoken'
import { logError } from '@core/log'
import { AuthUser } from '@modules/users/types/user'
import { findPoliciesByRoleAndUserId } from '@modules/users/repositories/policy'
import { extractPermissionsAndRestrictions } from '@modules/users/services/policy'
import { PeekRequest } from '@api/interfaces'
import {
  setAuthUser,
  setCurrentToken,
  setAclScopeQueryV2,
  setMcpScope
} from '@core/auth'
import { Response } from 'express'
import Sentry from '@sentry/node'
import UnauthorizedError from '@core/errors/unauthorizedError'
import ForbiddenError from '@core/errors/forbiddenError'
import { getEnvVariable } from '@core/util'
import { Tools } from '../types/tools'
import { RoleAliases } from '@modules/users/types/role'
import {
  verifyAuth0Token,
  extractUserContext,
  extractResourceIdsFromAllow
} from './utils'

export const authenticate = () => {
  return async (req: PeekRequest, res: Response, next: NextFunction) => {
    if (req.headers['mcp-api-key'] === getEnvVariable('MCP_API_KEY')) {
      setAclScopeQueryV2({
        communities: [],
        organizations: []
      })
      setMcpScope({
        tools: Object.values(Tools)
      })
      next()
      return
    }

    const authHeader = req.headers.authorization

    if (!authHeader) {
      throw new ForbiddenError('Missing Authorization header')
    }

    const parts = authHeader.split(' ')
    if (parts.length !== 2 || parts[0].toLowerCase() !== 'bearer') {
      throw new UnauthorizedError(
        "Invalid Authorization header format, expected 'Bearer TOKEN'"
      )
    }

    const token = parts[1].trim()

    const tokenParts = token.split('.')

    if (tokenParts.length === 5) {
      throw new UnauthorizedError(
        'Encrypted tokens (JWE) are not supported. Please configure Auth0 to use JWT (signed tokens only).'
      )
    } else if (tokenParts.length !== 3) {
      throw new UnauthorizedError('Invalid token format')
    }

    let decoded: JwtPayload

    try {
      decoded = await verifyAuth0Token(token)
    } catch (verifyError: any) {
      logError('mcp-auth', 'Token verification failed', {
        error: verifyError.message,
        name: verifyError.name
      })

      if (verifyError.name === 'TokenExpiredError') {
        throw new UnauthorizedError('Token expired')
      }

      throw new UnauthorizedError(verifyError.message || 'Invalid token')
    }

    const userContext = extractUserContext(decoded)

    if (!userContext.userId) {
      throw new ForbiddenError('Token missing user identification')
    }

    const { userId, ...context } = userContext
    const authUser: AuthUser = {
      ...context,
      _id: userId as string,
      allow: [],
      deny: []
    }

    const policies = await findPoliciesByRoleAndUserId(
      authUser.role._id.toString(),
      authUser._id.toString()
    )

    const { allow, deny } = extractPermissionsAndRestrictions(policies)

    authUser.allow = allow
    authUser.deny = deny

    req.authUser = authUser

    Sentry.setUser({
      id: authUser._id.toString(),
      allow: authUser.allow,
      deny: authUser.deny
    })

    setAuthUser(authUser)
    setMcpScope({
      tools: Object.values(Tools)
    })
    setCurrentToken(token)

    if (authUser.role.alias === RoleAliases.ADMIN) {
      setAclScopeQueryV2({
        communities: [],
        organizations: []
      })
      return next()
    }

    const communitiesFromAllow = extractResourceIdsFromAllow(
      authUser.allow,
      'communities'
    )
    const organizationsFromAllow = extractResourceIdsFromAllow(
      authUser.allow,
      'organizations'
    )

    setAclScopeQueryV2({
      communities: communitiesFromAllow,
      organizations: organizationsFromAllow
    })

    next()
  }
}
