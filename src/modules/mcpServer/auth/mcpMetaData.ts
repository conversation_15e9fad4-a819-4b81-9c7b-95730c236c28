import { Router, Request, Response } from 'express'

export const mcpMetaDataRoutes = Router()

const ISSUER = `https://${process.env.AUTH0_DOMAIN}`

mcpMetaDataRoutes.get(
  '/.well-known/oauth-authorization-server',
  async (req: Request, res: Response) => {
    try {
      const authServerMetadataUrl = `${ISSUER}/.well-known/openid-configuration`

      const response = await fetch(authServerMetadataUrl)
      if (!response.ok) {
        throw new Error(
          `Failed to fetch authorization server metadata: ${response.statusText}`
        )
      }

      const metadata = await response.json()

      res.setHeader('Content-Type', 'application/json')
      res.setHeader('Cache-Control', 'max-age=3600')

      res.json(metadata)
    } catch (error) {
      console.error('Error fetching authorization server metadata:', error)
      res.status(500).json({
        error: 'server_error',
        errorDescription: 'Failed to fetch authorization server metadata'
      })
    }
  }
)

export default mcpMetaDataRoutes
