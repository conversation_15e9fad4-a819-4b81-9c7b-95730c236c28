import { McpSpaceIn, Mcp<PERSON>paceInId, McpSpaceOut } from '../schemas'
import { buildSpaceQuery } from '../utils/queryBuilder'
import { spaceMcpOutProjection, spaceToMcpSpaceOut } from '../utils/spaceUtils'
import { SpaceType } from '@modules/communities/types/spaceType'
import { ProjectionOptions } from '@core/repositories/CrudRepository'
import { spaceRepositoryMongoDb } from '@modules/communities/repositories/v2/spaceRepository'
import { findSpaceByCoordinates } from '@modules/spacex/services/spaceService'
import { logDebug } from '@core/log'
import { SpaceToMcpOutInput } from '../types/space'

const location = 'mcp-spaces-service'

export const findSpaces = async (query: McpSpaceIn): Promise<McpSpaceOut[]> => {
  const { limit = 10, skip = 0 } = query

  logDebug(location, 'findSpaces called', { query })

  const queryObject = buildSpaceQuery(query, {
    isVisible: true, // isVisible is the same as available, don't need to filter
    isComplete: true, // don't need to be complete
    type: SpaceType.Unit
  })

  const spaces = (await spaceRepositoryMongoDb.findAll(queryObject, {
    projection: spaceMcpOutProjection as unknown as ProjectionOptions,
    limit,
    skip,
    populate: [
      {
        path: 'communityFull',
        select: '_id name tours'
      }
    ]
  })) as SpaceToMcpOutInput[]

  const result = spaces.map((space) => spaceToMcpSpaceOut(space))
  logDebug(location, 'findSpaces result', { count: result.length })
  return result
}

export const findSimilarSpaces = async ({
  _id
}: McpSpaceInId): Promise<McpSpaceOut[]> => {
  logDebug(location, 'findSimilarSpaces called', { _id })
  const similarSpaces = await findSpaceByCoordinates(_id)

  const spaces = (await spaceRepositoryMongoDb.findAll(
    { _id: { $in: similarSpaces.map((s) => s._id) } },
    {
      projection: spaceMcpOutProjection as unknown as ProjectionOptions,
      populate: [
        {
          path: 'communityFull',
          select: '_id name tours'
        }
      ]
    }
  )) as SpaceToMcpOutInput[]

  const result = spaces.map((space) => spaceToMcpSpaceOut(space))
  logDebug(location, 'findSimilarSpaces result', { count: result.length })
  return result
}
