import {
  buildProspectIntelligenceQuery,
  findProspectIntelligenceById,
  prospectActivityTourMapper
} from '@modules/analytics/services/prospectIntelligence'
import * as repo from '@modules/analytics/repositories/prospectIntelligence'
import { McpProspectIntelligenceIn } from '../schemas/mcpProspectIntelligenceIn'
import { ObjectId } from 'mongodb'
import { McpProspectActivitiesIn } from '../schemas/mcpProspectActivitiesIn'
import { ProspectActivities } from '@modules/analytics/types/prospectIntelligence'
import NotFoundError from '@core/errors/notFoundError'
import { McpPiActivityIn } from '../schemas/mcpPiActivityIn'
import { applyAclFilters } from '../utils'

const convertIdsToObjectId = (ids: string | string[]): ObjectId[] => {
  const idArray = Array.isArray(ids) ? ids : [ids]
  return idArray.map((id) => new ObjectId(id))
}

const buildQueryWithUserFilters = (params: any) => {
  const filteredParams = applyAclFilters(params)

  const queryParams = { ...filteredParams }

  if (queryParams.communityId) {
    const communityIds = convertIdsToObjectId(queryParams.communityId)
    queryParams.communityId = { $in: communityIds }
    delete filteredParams.communityId
  }

  if (queryParams.organizationId) {
    const organizationIds = convertIdsToObjectId(queryParams.organizationId)
    queryParams.organizationId = { $in: organizationIds }
    delete filteredParams.organizationId
  }

  return buildProspectIntelligenceQuery({
    ...filteredParams,
    ...queryParams
  })
}

export const getProspectsIntelligence = async (
  params: McpProspectIntelligenceIn
): Promise<any> => {
  const query = buildQueryWithUserFilters(params)

  const [data, totalCount] = await Promise.all([
    repo.getProspectsIntelligence(query),
    repo.countProspectsIntelligence(query)
  ])

  return {
    data,
    totalCount
  }
}

export const getPiActivities = async (
  params: McpProspectActivitiesIn
): Promise<any> => {
  const { prospectsMode, ...rest } = params
  const activity = Object.values(ProspectActivities).map((activity) =>
    activity.toLowerCase()
  )

  const newParams = {
    ...rest,
    activity
  }

  if (prospectsMode === 'new_prospects') {
    return getNewestProspectsActivities(newParams)
  }
  return getPiActivitiesDefault(newParams)
}

export const getPiActivitiesDefault = async (
  params: McpProspectActivitiesIn
): Promise<any> => {
  const query = buildQueryWithUserFilters(params)

  const [data, totalCount] = await Promise.all([
    repo.getProspectActivities(query),
    repo.countProspectActivities(query)
  ])

  return {
    data: prospectActivityTourMapper(data),
    totalCount
  }
}

export const getNewestProspectsActivities = async (
  params: McpProspectActivitiesIn
): Promise<any> => {
  const query = buildQueryWithUserFilters(params)

  const [data, totalCount] = await Promise.all([
    repo.getNewestProspectsRecentActivity(query),
    repo.countNewestProspectsRecentActivity(query)
  ])

  return {
    data: prospectActivityTourMapper(data),
    totalCount
  }
}

export const getRoomsViewed = async (params: {
  sessionId: string
  prospectEmail: string
  spaceId: string
}): Promise<any> => {
  const query = { ...params }
  const [data] = await repo.getRoomsViewed(query)
  return data || { total: 0, rooms: [] }
}

export const getPiActivity = async (params: McpPiActivityIn): Promise<any> => {
  const pi = await findProspectIntelligenceById(params.id)

  if (!pi) {
    throw new NotFoundError('Could not find prospect intelligence')
  }

  let roomsViewed = []
  if (pi.appId !== 'casa') {
    roomsViewed = await getRoomsViewed({
      sessionId: pi.event.sessionId,
      prospectEmail: pi.prospect.email,
      spaceId: pi.space._id
    })
  }

  return {
    ...pi,
    roomsViewed
  }
}
