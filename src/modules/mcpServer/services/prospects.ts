import { McpProspectIn } from '../schemas'
import { ProspectOrigins } from '@modules/prospects/types/prospect'
import { createProspect as createProspectService } from '@modules/prospects/services/prospect'
import { logDebug } from '@core/log'

const location = 'mcp-prospects-service'

export const createProspect = async (input: McpProspectIn) => {
  logDebug(location, 'createProspect called', { input })

  return {
    success: true,
    message: 'Prospect created'
  }
}
