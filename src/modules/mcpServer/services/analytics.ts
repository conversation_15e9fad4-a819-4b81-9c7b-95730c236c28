import { getFakeData } from '@core/fakeData'
import { McpDaywiseSpaceViewsIn } from '../schemas/mcpDaywiseSpaceViewsIn'
import { McpViewedSpacesIn } from '../schemas/mcpViewedSpacesIn'
import { McpCommunitiesAnalyticsIn } from '../schemas/mcpCommunitiesAnalyticsIn'
import { logDebug } from '@core/log'
import { McpProspectActivitiesIn } from '../schemas/mcpProspectActivitiesIn'

const location = 'mcp-analytics-service'

export const getDaywiseSpaceViews = async (
  input: McpDaywiseSpaceViewsIn
): Promise<any> => {
  logDebug(location, 'getDaywiseSpaceViews called', { input })
  return getFakeData('daywiseSpaceViews', input)
}

export const getViewedSpaces = async (
  input: McpViewedSpacesIn
): Promise<any> => {
  logDebug(location, 'getViewedSpaces called', { input })
  return getFakeData('viewedSpaces', input)
}

export const getCommunitiesAnalytics = async (
  input: McpCommunitiesAnalyticsIn
): Promise<any> => {
  logDebug(location, 'getCommunitiesAnalytics called', { input })
  return getFakeData('communities', input)
}
