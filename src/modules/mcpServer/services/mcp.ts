import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js'
import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { PeekRequest } from '@api/interfaces'
import { ServerResponse } from 'http'
import { logDebug } from '@core/log'

const SESSION_TTL_MS = 7 * 24 * 60 * 60 * 1000 // 7 days
const MAX_SESSIONS = 1000

export interface TransportEntry {
  transport: StreamableHTTPServerTransport
  lastUsed: number
}

/**
 * Creates (or retrieves) a StreamableHTTPServerTransport for the current HTTP session
 * and lets the MCP SDK handle the request.
 *
 * A Map is used to keep the transports alive between requests, allowing the
 * SDK to maintain conversational state. Idle transports should be purged by
 * the caller (see TODO comment).
 */
export const handleMcpRequest = async (
  mcpServer: Server,
  transports: Map<string, TransportEntry>,
  req: PeekRequest,
  res: ServerResponse
) => {
  cleanupTransports(transports)

  const headerValue = req.headers['mcp-session-id']
  const sessionId: string = typeof headerValue === 'string' ? headerValue : ''

  let entry: TransportEntry | undefined
  if (sessionId && transports.has(sessionId)) {
    entry = transports.get(sessionId)
  }

  if (!entry) {
    entry = await createTransport(mcpServer, transports, res)
  }

  entry.lastUsed = Date.now()
  await entry.transport.handleRequest(req, res, req.body)
}

async function createTransport(
  mcpServer: Server,
  transports: Map<string, TransportEntry>,
  res: ServerResponse
) {
  const transport = new StreamableHTTPServerTransport({
    sessionIdGenerator: undefined,
    enableJsonResponse: true,
    onsessioninitialized: (sessionId: string) => {
      transports.set(sessionId, { transport, lastUsed: Date.now() })
      res.setHeader('mcp-session-id', sessionId)
    }
  })

  transport.onclose = () => transports.delete(transport.sessionId as string)

  await mcpServer.connect(transport)

  return { transport, lastUsed: Date.now() }
}

function cleanupTransports(transports: Map<string, TransportEntry>) {
  const now = Date.now()
  for (const [id, entry] of Array.from(transports.entries())) {
    if (now - entry.lastUsed > SESSION_TTL_MS) {
      transports.delete(id)
      entry.transport.close?.()
      logDebug('mcp-transport', 'Evicted idle transport', { id })
    }
  }

  if (transports.size > MAX_SESSIONS) {
    const sorted = Array.from(transports.entries()).sort(
      (a, b) => a[1].lastUsed - b[1].lastUsed
    )
    const toRemove = sorted.slice(0, transports.size - MAX_SESSIONS)
    for (const [id, entry] of toRemove) {
      transports.delete(id)
      entry.transport.close?.()
      logDebug('mcp-transport', 'Evicted transport to respect maxSessions', {
        id
      })
    }
  }
}
