import { Router } from 'express'
import { AxiosError } from 'axios'
import authenticate from '@api/middlewares/auth'
import { logError, logInfo, logWarn } from '@core/log'
import { makeInternalApiV2Request } from '@core/request/peekInternalApiClient'
import acl from '@api/middlewares/acl'

const router = Router()

const methods: string[] = ['GET', 'POST']

methods.forEach((method) => {
  router[method.toLowerCase()](
    '*',
    authenticate(),
    acl(['peek:users:<userId>::insights_access']),
    async (req, res) => {
      try {
        const result = await makeInternalApiV2Request(
          `/insights${req.path}`,
          method as 'GET' | 'POST',
          undefined,
          undefined,
          req.query as Record<string, string>
        )

        logInfo('InsightsProxy', 'insights response', { response: result })

        res.status(200).json(result)
      } catch (error) {
        if (error instanceof AxiosError) {
          logWarn('insightsProxy', 'insights error', {
            error: error.response?.data
          })
          res
            .status(error.response?.status || 500)
            .json({ error: error.response?.data })
          return
        }
        logError('insightsProxy', 'insights error', { error: error.message })
        res.status(500).json({ error: error.message })
      }
    }
  )
})

export default router
