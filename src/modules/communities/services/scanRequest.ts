import { getAuthUser } from '@core/auth'
import BadRequestError from '@core/errors/badRequestError'
import NotFoundError from '@core/errors/notFoundError'
import { logDebug, logError, logInfo, logWarn } from '@core/log'
import { KLAVIYO_EVENTS, sendMail } from '@core/mail'
import { PaginatedResult } from '@core/types'
import { getEnvVariable } from '@core/util'
import { findRolesByQuery } from '@modules/users/repositories/role'
import { findUsers } from '@modules/users/repositories/user'
import { findUserByEmail, findUserById } from '@modules/users/services/user'
import { RoleAliases } from '@modules/users/types/role'
import { User, UserTag } from '@modules/users/types/user'
import { format, isSameDay, isValid } from 'date-fns'
import {
  findCommunityById,
  findCommunityByIdLean
} from '../repositories/community'
import * as scanRequestRepo from '../repositories/scanRequest'
import { findScanRequestsByQuery } from '../repositories/scanRequest'
import { findSpacesByQuery } from '../repositories/space'
import { Community, planFeatures } from '../types/community'
import { ObjectId } from '../types/id'
import {
  CreateScanRequestsDTO,
  DateField,
  FindScanRequestsCountByCommunitiesDTO,
  FindScanRequestsDTO,
  GroupScanRequest,
  ScanRequest,
  ScanRequestStatus
} from '../types/scanRequest'
import { Space } from '../types/space'
import {
  buildAddresses,
  buildBaseEmailParams,
  buildScannerEmailParams,
  EmailParams,
  filterDuplicateScanRequests,
  formatCanceledScanRequestDateString,
  formatScanRequestDateString,
  formatScanRequests,
  getCalendar,
  getJobId,
  GetListResponse,
  isRequestedByAdmin,
  maskRequestedBy,
  RescheduledEmailParams,
  ScanRequestStatusList,
  sendRescheduledEmail,
  sendScannerConfirmationEmail,
  sendUserConfirmationEmail,
  validateScheduledRequests,
  validateUsers
} from './scanRequestUtils'
import { findSpaceById } from './space'

const location = 'src/modules/communities/services/scanRequest.ts'

/**
 * createScanRequests
 * Creates new scan requests for specified spaces in a community
 * @param createScanRequests - DTO containing community, spaces, and scheduling information
 * @returns Promise<ScanRequest[]> - Array of created scan requests
 */
export const createScanRequests = async (
  createScanRequests: CreateScanRequestsDTO
): Promise<ScanRequest[]> => {
  logInfo(location, 'Creating scan requests.', createScanRequests)
  const requestedAt = new Date()

  const community: Pick<
    Community,
    '_id' | 'name' | 'organization' | 'metroArea' | 'features'
  > = await findCommunityById(
    createScanRequests.community._id.toString(),
    'organization name _id metroArea features'
  )
  if (!community) throw new NotFoundError('Community not found.')
  const user = getAuthUser()

  let isScheduled = false
  let scheduledAt: Date | null = null
  if (
    createScanRequests.status === ScanRequestStatus.SCHEDULED ||
    isValid(new Date(createScanRequests.scheduledFor))
  ) {
    isScheduled = true
    scheduledAt = new Date()
  }

  const currentScheduledForStart = isValid(
    new Date(createScanRequests.scheduledFor)
  )
    ? new Date(createScanRequests.scheduledFor)
    : new Date()

  currentScheduledForStart.setHours(0, 0, 0, 0)
  const currentScheduledForEnd = new Date(currentScheduledForStart)
  currentScheduledForEnd.setHours(23, 59, 59, 999)

  const now = new Date()
  now.setHours(0, 0, 0, 0)

  if (currentScheduledForStart < now) {
    throw new BadRequestError('Scheduled for date cannot be in the past.')
  }

  const existingCompletedWithSameDate = (
    await Promise.all(
      createScanRequests.spaces.map((space) =>
        scanRequestRepo.findScanRequestByQuery({
          'space._id': space._id.toString(),
          status: ScanRequestStatus.COMPLETED,
          scheduledFor: {
            $gte: currentScheduledForStart,
            $lt: currentScheduledForEnd
          }
        })
      )
    )
  ).filter((scanRequest) => scanRequest)

  const existingRequestedOrScheduled =
    await scanRequestRepo.findScanRequestsByQuery({
      'space._id': {
        $in: createScanRequests.spaces.map((space) => space._id.toString())
      },
      status: {
        $in: [ScanRequestStatus.REQUESTED, ScanRequestStatus.SCHEDULED]
      }
    })

  const existingScanRequestsForSpaces = [
    ...existingCompletedWithSameDate,
    ...existingRequestedOrScheduled
  ]

  if (existingScanRequestsForSpaces?.length > 0) {
    createScanRequests.spaces = createScanRequests.spaces.filter(
      (space) =>
        !existingScanRequestsForSpaces.some(
          (scanRequest) =>
            scanRequest.space._id.toString() === space._id.toString()
        )
    )
  }

  if (createScanRequests?.spaces?.length <= 0) {
    return existingScanRequestsForSpaces
  }

  const spacesRequests = []
  for (const space of createScanRequests.spaces) {
    spacesRequests.push(
      findSpace(
        space._id.toString(),
        '_id type unit building bedrooms bathrooms floorPlan availabilityStatus unitSize isComplete token tourLastUpdatedDate'
      )
    )
  }
  const spaces: Space[] = await Promise.all(spacesRequests)

  let status = createScanRequests.status
  if (!status) {
    status = createScanRequests.scheduledFor
      ? ScanRequestStatus.SCHEDULED
      : ScanRequestStatus.REQUESTED
  }

  const scanRequest = createScanRequests.spaces.map((_, i) => {
    const spaceAvailabilityWhenRequested =
      spaces[i].availabilityStatus || undefined
    const spaceAvailabilityWhenScheduled = isScheduled
      ? spaces[i].availabilityStatus
      : undefined

    return {
      ...createScanRequests,
      community,
      requestedBy: {
        _id: user._id,
        name: user.name,
        email: user.email
      },
      requestedAt,
      space: spaces[i],
      status,
      isAutoScan:
        user.roleAlias === RoleAliases?.ADMIN &&
        community?.features?.includes(planFeatures.AutoScan),
      ...(scheduledAt && { scheduledAt }),
      ...(spaceAvailabilityWhenRequested && { spaceAvailabilityWhenRequested }),
      ...(spaceAvailabilityWhenScheduled && { spaceAvailabilityWhenScheduled }),
      ...(isScheduled && { scheduledFor: currentScheduledForStart }),
      isRescan: spaces[i].isComplete,
      parrotsRequest: isRequestedByAdmin(scanRequest)
    }
  })

  const result = (await scanRequestRepo.createScanRequests(
    scanRequest
  )) as ScanRequest[]

  result.push(...existingScanRequestsForSpaces)

  logInfo(location, 'Created scan requests.', result)

  return result
}

/**
 * findScanRequests
 * Retrieves scan requests based on query parameters with pagination
 * @param query - Query parameters for filtering scan requests
 * @returns Promise<PaginatedResult<ScanRequest | GroupScanRequest>> - Paginated scan requests
 */
export const findScanRequests = async (
  query: FindScanRequestsDTO
): Promise<PaginatedResult<ScanRequest | GroupScanRequest>> => {
  if (query.startDate) {
    query.dateField = query.dateField || DateField.ScannedAt
    query.startDate = new Date(query.startDate)
  }

  if (query.endDate) {
    query.dateField = query.dateField || DateField.ScannedAt
    query.endDate = query.endDate ? new Date(query.endDate) : new Date()
  }

  if (query.spaceId || query['space._id']) {
    query['space._id'] = new ObjectId(query.spaceId || query['space._id'])
    delete query.spaceId
  }

  const result = await scanRequestRepo.findScanRequests(query)
  const user = getAuthUser()
  if (user.roleAlias !== RoleAliases.ADMIN) {
    result.data = result.data.map((scanRequest) => ({
      ...scanRequest,
      requestedBy: maskRequestedBy(scanRequest)
    }))
  }
  if (query.orderBy === 'scheduledFor') {
    result.data.forEach((scanRequest, i) => {
      if (!scanRequest.scheduledFor) {
        result.data.splice(i, 1)
        if (query.order === 'asc' || !query.order) {
          result.data.push(scanRequest)
        } else {
          result.data.unshift(scanRequest)
        }
      }
    })
  }

  return result
}

/**
 * updatedScanRequestById
 * Updates a scan request by ID with different handling based on status
 * @param _id - The scan request ID to update
 * @param updateScanRequest - Partial scan request data for update
 * @returns Promise<ScanRequest> - Updated scan request
 */
export const updatedScanRequestById = async (
  _id: string,
  updateScanRequest: Partial<ScanRequest>
): Promise<ScanRequest> => {
  let result: ScanRequest
  switch (updateScanRequest.status) {
    case ScanRequestStatus.COMPLETED:
      result = await completeScanRequest(_id)
      break
    case ScanRequestStatus.CANCELED:
      result = await cancelScanRequest(_id, updateScanRequest.reason)
      break
    case ScanRequestStatus.SKIPPED:
      result = await skipScanRequest(
        _id,
        updateScanRequest.reason,
        null,
        updateScanRequest.comment
      )
      break
    case ScanRequestStatus.SCHEDULED:
      result = await scheduleScanRequest(
        _id,
        updateScanRequest.scheduledFor,
        updateScanRequest.scannerArrivalWindow,
        updateScanRequest.scannerEmail
      )
      break
    default:
      result = await scanRequestRepo.updateScanRequest(
        { _id },
        updateScanRequest
      )
      break
  }

  return result
}

/**
 * updateAllScanRequestByCommunityId
 * Updates all scan requests for a specific community
 * @param communityId - The community ID to update scan requests for
 * @param updateData - Partial scan request data for update
 * @returns Promise<number> - Number of updated scan requests
 */
export const updateAllScanRequestByCommunityId = async (
  communityId: string,
  updateData: Partial<ScanRequest>
): Promise<number> => {
  return scanRequestRepo.updateAllScanRequestByCommunityId(
    communityId,
    updateData
  )
}

/**
 * updateScanRequests
 * Updates multiple scan requests and handles canceled request email notifications
 * @param scanRequests - Array of partial scan request data for updates
 * @returns Promise<ScanRequest[]> - Array of updated scan requests
 */
export const updateScanRequests = async (
  scanRequests: Partial<ScanRequest>[]
) => {
  const canceledScanRequests = scanRequests.filter(
    (scanRequest) => scanRequest.status === ScanRequestStatus.CANCELED
  )

  let canceledScanRequestsBeforeUpdate: ScanRequest[] = []
  if (canceledScanRequests?.length) {
    canceledScanRequestsBeforeUpdate = await findScanRequestsByQuery({
      _id: { $in: canceledScanRequests.map((sr) => sr._id.toString()) }
    })
  }

  const result = await Promise.all(
    scanRequests.map((scanRequest) =>
      updatedScanRequestById(scanRequest._id.toString(), scanRequest)
    )
  )

  if (canceledScanRequestsBeforeUpdate?.length) {
    await sendCanceledScanRequestEmails(canceledScanRequestsBeforeUpdate)
  }

  return result
}

/**
 * sendCanceledScanRequestEmails
 * Sends email notifications for canceled scan requests to recipients
 * @param scanRequests - Array of canceled scan requests
 * @returns Promise<void>
 */
const sendCanceledScanRequestEmails = async (
  scanRequests: ScanRequest[]
): Promise<void> => {
  if (!scanRequests?.length) return

  const requests = scanRequests.filter(
    (sr) => sr.status === ScanRequestStatus.SCHEDULED
  )

  if (requests.length === 0) {
    logDebug(
      location,
      'E-mail not sent because the scan request has not been scheduled yet.'
    )
    return
  }

  const recipients = await getRecipients(
    requests[0].community._id.toString(),
    [
      RoleAliases.AGENT,
      RoleAliases.ORG_ADMIN,
      RoleAliases.BASIC,
      RoleAliases.OWNER
    ],
    { receiveCanceledRequestsNotification: true }
  )
  logDebug(
    location,
    'Recipients found.',
    recipients.map((r) => r.email)
  )
  if (recipients?.length) {
    const scanRequestWithJobId = requests.find((sr: ScanRequest) =>
      getJobId(sr)
    )

    let data = {} as GetListResponse

    if (scanRequestWithJobId?.jobId || scanRequestWithJobId?.scheduledFor) {
      data = await getScanAndUpdateList(scanRequestWithJobId.jobId, {
        forList: ScanRequestStatus.SCHEDULED,
        forUpdate: ScanRequestStatus.CANCELED
      })
    } else {
      data.updatedList = formatScanRequests(requests)
    }

    const calendarLinks = getCalendar(requests[0])
    const formattedDateString = formatCanceledScanRequestDateString(requests[0])
    const promises = recipients.map((recipient) =>
      sendMail(recipient.email, KLAVIYO_EVENTS.SCAN_REQUESTS_CANCELED, {
        communityName: requests[0].community.name,
        scansList: data.scansList ?? [],
        updatedList: data.updatedList ?? [],
        scansListLength: data.scansList?.length,
        updatedListLength: data.updatedList?.length,
        scanRequestsDashboardURL: `${process.env.AGENT_DASHBOARD_WEB_URL}/scan-requests/add-new-request`,
        userName: recipient.name,
        dateString: formattedDateString,
        scheduledFor:
          requests[0]?.scheduledFor &&
          format(requests[0]?.scheduledFor, 'MM/dd/yyyy'),
        appleCalendarLink: calendarLinks?.apple,
        googleCalendarLink: calendarLinks?.google,
        outlookCalendarLink: calendarLinks?.outlook
      })
    )
    const promisesResult = await Promise.allSettled(promises)
    promisesResult.forEach((promise) => {
      if (promise.status === 'fulfilled') {
        logDebug(location, 'Email sent successfully.', {
          data: promise
        })
      } else {
        logWarn(location, `Error sending email ${promise.reason}.`)
      }
    })
  }
}

/**
 * handleRescheduledRequests
 * Handles email notifications for rescheduled scan requests
 * @param rescheduledScanRequests - Array of rescheduled scan requests
 * @param ids - Array of scan request IDs
 * @param users - Array of users to notify
 * @param baseParams - Base email parameters
 * @returns Promise<void>
 */
const handleRescheduledRequests = async (
  rescheduledScanRequests: ScanRequest[],
  ids: string[],
  users: User[],
  baseParams: EmailParams
): Promise<void> => {
  if (!rescheduledScanRequests.length) return

  /**
   * A job id is based on the community and the scheduled for date - it is used to identify scan requests made for the same day
   * We can call a group of scan requests with the same job as a 'photoshoot'
   */
  const previousJobId = `${rescheduledScanRequests[0].community._id.toString()}-${format(
    rescheduledScanRequests[0].previousScheduledForDate,
    'yyyyMMdd'
  )}`

  // Find all scan requests that are still scheduled
  const stillScheduledScanRequests =
    await scanRequestRepo.findScanRequestsByQuery({
      _id: { $nin: ids.map((id: string) => new ObjectId(id)) },
      deletedAt: null,
      jobId: previousJobId,
      status: ScanRequestStatus.SCHEDULED
    })

  // Format scan requests lists
  const stillScheduledList = formatScanRequests(stillScheduledScanRequests)
  let rescheduledList = formatScanRequests(rescheduledScanRequests)

  // Filter out rescheduled requests that are still scheduled
  rescheduledList = filterDuplicateScanRequests(
    rescheduledList,
    stillScheduledList
  )

  const scannerArrivalWindow = `The scanner will arrive between ${rescheduledScanRequests[0]?.scannerArrivalWindow?.from} and ${rescheduledScanRequests[0]?.scannerArrivalWindow?.to}.`

  const rescheduledParams: RescheduledEmailParams = {
    ...baseParams,
    scansList: stillScheduledList,
    scansListLength: stillScheduledList.length,
    updatedList: rescheduledList,
    updatedListLength: rescheduledList.length,
    originalScheduledForDate: format(
      rescheduledScanRequests[0].previousScheduledForDate,
      'MM/dd/yyyy'
    ),
    newScheduledForDate: format(
      rescheduledScanRequests[0].scheduledFor,
      'MM/dd/yyyy'
    ),
    scannerArrivalWindow
  }

  await Promise.all(
    users.map((user) => sendRescheduledEmail(user, rescheduledParams))
  )
}

/**
 * processExistingScanRequests
 * Processes existing scan requests to separate rescheduled and existing ones
 * @param jobId - Job ID for grouping scan requests
 * @param scheduledRequests - Array of scheduled scan requests
 * @param ids - Array of scan request IDs to exclude
 * @returns Promise<{existingScanRequests: ScanRequest[], rescheduledScanRequests: ScanRequest[]}> - Separated scan requests
 */
const processExistingScanRequests = async (
  jobId: string,
  scheduledRequests: ScanRequest[],
  ids: string[]
): Promise<{
  existingScanRequests: ScanRequest[]
  rescheduledScanRequests: ScanRequest[]
}> => {
  if (!jobId) {
    return { existingScanRequests: [], rescheduledScanRequests: [] }
  }

  const allExistingRequests = await scanRequestRepo.findScanRequestsByQuery({
    deletedAt: null,
    jobId: scheduledRequests[0].jobId,
    status: ScanRequestStatus.SCHEDULED
  })

  const rescheduledScanRequests = allExistingRequests.filter(
    (sr: ScanRequest) => sr.isRescheduled
  )

  const existingScanRequests = allExistingRequests.filter(
    (sr: ScanRequest) => !sr.isRescheduled && !ids.includes(sr._id.toString())
  )

  return { existingScanRequests, rescheduledScanRequests }
}

/**
 * sendScannerEmails
 * Sends confirmation emails to scanners for scheduled requests
 * @param scheduledRequests - Array of scheduled scan requests
 * @returns Promise<void>
 */
const sendScannerEmails = async (
  scheduledRequests: ScanRequest[]
): Promise<void> => {
  logInfo(location, 'Sending scheduled confirmation emails to scanners.')

  const groupedScheduledRequests = scheduledRequests.reduce((acc, request) => {
    if (!request.scannerEmail) return acc
    if (!acc[request.scannerEmail]) acc[request.scannerEmail] = []
    acc[request.scannerEmail].push(request)
    return acc
  }, {} as { [key: string]: ScanRequest[] })

  const scannerEmailPromises = Object.entries(groupedScheduledRequests).map(
    async ([scannerEmail, scanRequests]) => {
      if (!scanRequests[0].scannerEmail) return

      try {
        const scanner = await findUserByEmail(scanRequests[0].scannerEmail)
        if (!scanner) {
          logWarn(location, `Scanner not found for email: ${scannerEmail}`)
          return
        }

        const community = await findCommunityByIdLean(
          scanRequests[0].community._id.toString()
        )
        if (!community) {
          logWarn(
            location,
            `Community not found for scanner email: ${scannerEmail}`
          )
          return
        }

        const addresses = buildAddresses(scanRequests[0])
        const scannerParams = buildScannerEmailParams(
          scanner,
          scanRequests,
          community,
          addresses.building,
          addresses.community
        )

        await sendScannerConfirmationEmail(scannerEmail, scannerParams)
      } catch (error) {
        logError(
          location,
          `Error processing scanner email for ${scannerEmail}`,
          { error, scannerEmail }
        )
      }
    }
  )

  await Promise.allSettled(scannerEmailPromises)
}

/**
 * completeScanRequest
 * Marks a scan request as completed with optional upload flag
 * @param id - The scan request ID to complete
 * @param isUpload - Optional flag indicating if this is an upload completion
 * @returns Promise<ScanRequest> - Completed scan request
 */
export const completeScanRequest = async (id: string, isUpload?: boolean) => {
  const scanRequest = await scanRequestRepo.findScanRequestById(id)
  if (!scanRequest) throw new NotFoundError('Scan request not found.')
  if (scanRequest.status === ScanRequestStatus.CANCELED) {
    logWarn(
      location,
      'Tried to COMPLETE a CANCELED scan request.',
      JSON.stringify(scanRequest)
    )
    return scanRequest
  }
  if (scanRequest.status === ScanRequestStatus.SKIPPED) {
    logWarn(
      location,
      'Tried to COMPLETE a SKIPPED scan request.',
      JSON.stringify(scanRequest)
    )
    return scanRequest
  }
  if (scanRequest.status === ScanRequestStatus.COMPLETED && !isUpload) {
    logInfo(
      location,
      'Tried to COMPLETE an already COMPLETED scan request.',
      JSON.stringify(scanRequest)
    )
    return scanRequest
  }

  let authUser = getAuthUser() as User
  if (!authUser) {
    const workLogJanitorId = getEnvVariable('WORKLOG_JANITOR_ID')
    authUser = await findUserById(workLogJanitorId)
  }
  const scannedBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }

  const spaceAvailabilityStatus = (
    await findSpaceById(scanRequest.space._id.toString(), 'availabilityStatus')
  )?.availabilityStatus

  return await scanRequestRepo.updateScanRequest(
    { _id: id },
    {
      status: ScanRequestStatus.COMPLETED,
      scannedAt: new Date(),
      scannedBy,
      ...(isUpload && { uploadedAt: new Date() }),
      ...(isUpload && {
        uploadedBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        }
      }),
      ...(!scanRequest.scheduledFor && {
        scheduledFor: new Date()
      }),
      ...(!scanRequest.scheduledBy && {
        scheduledBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        }
      }),
      ...(spaceAvailabilityStatus && {
        spaceAvailabilityWhenCompleted: spaceAvailabilityStatus
      }),
      ...(spaceAvailabilityStatus && {
        spaceAvailabilityWhenCompleted: spaceAvailabilityStatus
      })
    }
  )
}

/**
 * sendScheduledConfirmation
 * Sends confirmation emails for scheduled scan requests to users and scanners
 * @param ids - Array of scan request IDs to send confirmations for
 * @returns Promise<ScanRequest[]> - Array of scheduled scan requests
 */
export const sendScheduledConfirmation = async (ids: string[]) => {
  // Input validation
  if (!ids || ids.length === 0) {
    throw new BadRequestError('No scan request IDs provided.')
  }

  // Fetch and validate scheduled requests
  const scheduledRequests = await scanRequestRepo.findScanRequestsByQuery({
    _id: { $in: ids }
  })
  validateScheduledRequests(scheduledRequests)

  const firstRequest = scheduledRequests[0]
  const jobId = scheduledRequests.find((sr: ScanRequest) => getJobId(sr))?.jobId

  // Process existing scan requests
  const { existingScanRequests, rescheduledScanRequests } =
    await processExistingScanRequests(jobId, scheduledRequests, ids)

  // Get recipients
  const users = await getRecipients(
    firstRequest.community._id.toString(),
    [
      RoleAliases.AGENT,
      RoleAliases.ORG_ADMIN,
      RoleAliases.BASIC,
      RoleAliases.OWNER
    ],
    { receiveScanScheduledConfirmation: true }
  )
  validateUsers(users)

  // Format scan requests
  const updatedList = formatScanRequests(existingScanRequests)
  const scansList = formatScanRequests(scheduledRequests)

  // Build email parameters
  const formattedDateString = formatScanRequestDateString(firstRequest)
  const calendarLinks = getCalendar(firstRequest)

  const klaviyoTemplate =
    existingScanRequests?.length > 0
      ? KLAVIYO_EVENTS.SCAN_REQUESTS_ADDED
      : KLAVIYO_EVENTS.USER_SCHEDULED_SCANS_CONFIRMATION

  // Send emails to users
  await Promise.all(
    users.map(async (user: User) => {
      let params = buildBaseEmailParams(
        firstRequest,
        scansList,
        updatedList,
        user.name,
        calendarLinks,
        formattedDateString
      ) as EmailParams

      // Adjust parameters for SCAN_REQUESTS_ADDED template
      if (klaviyoTemplate === KLAVIYO_EVENTS.SCAN_REQUESTS_ADDED) {
        params = {
          ...params,
          scansList: updatedList,
          scansListLength: updatedList.length,
          updatedList: scansList,
          updatedListLength: scansList.length
        }
      }

      // Send confirmation email if conditions are met
      if (
        existingScanRequests?.length ||
        (!existingScanRequests.length && rescheduledScanRequests?.length === 0)
      ) {
        await sendUserConfirmationEmail(user, klaviyoTemplate, params)
      }
    })
  )

  // Handle rescheduled requests
  if (rescheduledScanRequests.length > 0) {
    const baseParams = buildBaseEmailParams(
      firstRequest,
      scansList,
      updatedList,
      '', // userName will be set per user
      calendarLinks,
      formattedDateString
    )
    await handleRescheduledRequests(
      rescheduledScanRequests,
      ids,
      users,
      baseParams
    )
  }

  logInfo(location, 'Sent scheduled confirmation emails to users.')

  // Send emails to scanners
  await sendScannerEmails(scheduledRequests)

  return scheduledRequests
}

/**
 * markScanRequestAsUploaded
 * Marks scan requests as uploaded for a specific space and date
 * @param spaceId - The space ID to mark as uploaded
 * @param scheduledFor - The scheduled date for the scan
 * @param user - Optional user performing the upload
 * @returns Promise<void>
 */
export const markScanRequestAsUploaded = async (
  spaceId: string,
  scheduledFor: Date,
  user?: User
) => {
  logInfo(location, 'Marking scan request as uploaded.', {
    spaceId,
    scheduledFor
  })

  const sodScheduledFor = new Date(scheduledFor)
  sodScheduledFor.setHours(0, 0, 0, 0)

  const scanRequests = await scanRequestRepo.findScanRequestsByQuery({
    'space._id': spaceId
  })

  const authUser = (getAuthUser() as User) || user
  const uploadedBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }
  const space = await findSpaceById(spaceId)

  if (scanRequests) {
    logInfo(location, 'Scan requests found: ', JSON.stringify(scanRequests))

    const onlySkippedOrCanceled = scanRequests.every(
      (scanRequest) =>
        scanRequest.status === ScanRequestStatus.SKIPPED ||
        scanRequest.status === ScanRequestStatus.CANCELED
    )
    if (onlySkippedOrCanceled) {
      logInfo(
        location,
        'All scan requests are skipped or canceled. Creating new scan request.'
      )
      const newScanRequest = await scanRequestRepo.createScanRequests([
        {
          space,
          status: ScanRequestStatus.COMPLETED,
          uploadedAt: new Date(),
          uploadedBy,
          accessDetails: '',
          availableFrom: new Date(),
          community: {
            _id: space.community._id,
            name: space.community.name,
            organization: space.community.organization
          },
          isAutoScan: false,
          specialInstructions: '',
          requestedBy: {
            _id: authUser._id,
            name: authUser.name,
            email: authUser.email
          },
          scannedBy: {
            _id: authUser._id,
            name: authUser.name,
            email: authUser.email
          },
          scannedAt: new Date(),
          scheduledFor: sodScheduledFor,
          scheduledBy: {
            _id: authUser._id,
            name: authUser.name,
            email: authUser.email
          },
          spaceAvailabilityWhenCompleted: space.availabilityStatus,
          comment: 'Scan request created automatically for uploaded space.'
        }
      ])
      logInfo(
        location,
        'Created new scan request.',
        JSON.stringify(newScanRequest)
      )
    } else {
      logInfo(location, 'At least one scan request is not skipped or canceled.')

      let scanRequestToNotSkip: ScanRequest | undefined

      const completedWithSameDate = scanRequests.find(
        (scanRequest) =>
          scanRequest.status === ScanRequestStatus.COMPLETED &&
          isSameDay(new Date(scanRequest.scheduledFor), sodScheduledFor)
      )
      if (completedWithSameDate) {
        logInfo(
          location,
          'At least one scan request is completed for the same day.'
        )
        const updatedScanRequest = await completeScanRequest(
          completedWithSameDate._id.toString(),
          true
        )
        scanRequestToNotSkip = updatedScanRequest
        logInfo(
          location,
          'Marked scan request as uploaded.',
          JSON.stringify(updatedScanRequest)
        )
      }
      const scheduledWithSameDate = scanRequests.find(
        (scanRequest) =>
          scanRequest.status === ScanRequestStatus.SCHEDULED &&
          isSameDay(new Date(scanRequest.scheduledFor), sodScheduledFor)
      )

      if (!completedWithSameDate && scheduledWithSameDate) {
        logInfo(
          location,
          'At least one scan request is scheduled for the same day.'
        )
        const updatedScanRequest = await completeScanRequest(
          scheduledWithSameDate._id.toString(),
          true
        )
        scanRequestToNotSkip = updatedScanRequest
        logInfo(
          location,
          'Marked scan request as uploaded.',
          JSON.stringify(updatedScanRequest)
        )
      }

      if (!completedWithSameDate && !scheduledWithSameDate) {
        logInfo(
          location,
          'No scan request is completed or scheduled for the same day. Finding the most recent scheduled'
        )
        const lastScheduledScanRequest = scanRequests
          .filter(
            (scanRequest) => scanRequest.status === ScanRequestStatus.SCHEDULED
          )
          .sort(
            (a, b) => b.scheduledFor.getTime() - a.scheduledFor.getTime()
          )[0]

        if (lastScheduledScanRequest) {
          logInfo(
            location,
            'Found the most recent scheduled scan request.',
            JSON.stringify(lastScheduledScanRequest)
          )
          const updatedScanRequest = await completeScanRequest(
            lastScheduledScanRequest._id.toString(),
            true
          )
          scanRequestToNotSkip = updatedScanRequest
          logInfo(
            location,
            'Marked scan request as uploaded.',
            JSON.stringify(updatedScanRequest)
          )
        } else {
          logInfo(
            location,
            'No scan request is scheduled. Finding the most recent requested.'
          )

          const lastRequestedScanRequest = scanRequests
            .filter(
              (scanRequest) =>
                scanRequest.status === ScanRequestStatus.REQUESTED
            )
            .sort(
              (a, b) => b.requestedAt.getTime() - a.requestedAt.getTime()
            )[0]
          if (lastRequestedScanRequest) {
            logInfo(
              location,
              'Found the most recent requested scan request.',
              JSON.stringify(lastRequestedScanRequest)
            )
            const updatedScanRequest = await completeScanRequest(
              lastRequestedScanRequest._id.toString(),
              true
            )
            scanRequestToNotSkip = updatedScanRequest
            logInfo(
              location,
              'Marked scan request as uploaded.',
              JSON.stringify(updatedScanRequest)
            )
          } else {
            logInfo(
              location,
              'No scan request is requested. Creating new scan request.'
            )
            const newScanRequest = await scanRequestRepo.createScanRequests([
              {
                space,
                status: ScanRequestStatus.COMPLETED,
                uploadedAt: new Date(),
                uploadedBy,
                accessDetails: '',
                availableFrom: new Date(),
                community: {
                  _id: space.community._id,
                  name: space.community.name,
                  organization: space.community.organization
                },
                isAutoScan: false,
                specialInstructions: '',
                requestedBy: {
                  _id: authUser._id,
                  name: authUser.name,
                  email: authUser.email
                },
                scannedBy: {
                  _id: authUser._id,
                  name: authUser.name,
                  email: authUser.email
                },
                scannedAt: new Date(),
                scheduledFor: sodScheduledFor,
                scheduledBy: {
                  _id: authUser._id,
                  name: authUser.name,
                  email: authUser.email
                },
                comment:
                  'Scan request created automatically for uploaded space.',
                spaceAvailabilityWhenCompleted: space.availabilityStatus
              }
            ])
            logInfo(
              location,
              'Created new scan request.',
              JSON.stringify(newScanRequest)
            )
          }
        }
      }

      if (scanRequestToNotSkip) {
        logInfo(location, 'Skipping all other scan requests.')
        const resp = await Promise.all(
          scanRequests
            .filter(
              (scanRequest) =>
                scanRequest._id.toString() !==
                  scanRequestToNotSkip._id.toString() &&
                scanRequest.status !== ScanRequestStatus.COMPLETED &&
                scanRequest.status !== ScanRequestStatus.CANCELED &&
                scanRequest.status !== ScanRequestStatus.SKIPPED
            )
            .map((scanRequest) =>
              skipScanRequest(scanRequest._id.toString(), 'Unknown', authUser)
            )
        )
        logInfo(
          location,
          'Skipped all other scan requests.',
          JSON.stringify(resp)
        )
      }
    }
  } else {
    logInfo(location, 'Scan request not found. Creating new scan request.')
    const newScanRequest = await scanRequestRepo.createScanRequests([
      {
        space,
        status: ScanRequestStatus.COMPLETED,
        uploadedAt: new Date(),
        uploadedBy,
        accessDetails: '',
        availableFrom: new Date(),
        community: {
          _id: space.community._id,
          name: space.community.name,
          organization: space.community.organization
        },
        isAutoScan: false,
        specialInstructions: '',
        requestedBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        },
        scannedBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        },
        scannedAt: new Date(),
        scheduledFor: sodScheduledFor,
        scheduledBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        },
        comment: 'Scan request created automatically for uploaded space.',
        spaceAvailabilityWhenCompleted: space.availabilityStatus
      }
    ])
    logInfo(
      location,
      'Created new scan request.',
      JSON.stringify(newScanRequest)
    )
    return
  }
}

/**
 * cancelScanRequest
 * Cancels a scan request with a specified reason
 * @param id - The scan request ID to cancel
 * @param reason - Optional reason for cancellation
 * @returns Promise<ScanRequest> - Canceled scan request
 */
export const cancelScanRequest = async (id: string, reason?: string) => {
  logInfo(location, 'Canceling scan request.', { scanRequestId: id, reason })
  if (!reason) throw new BadRequestError('Reason is required.')
  const scanRequest = await scanRequestRepo.findScanRequestById(id)
  if (!scanRequest) throw new NotFoundError('Scan request not found.')
  const authUser = getAuthUser() as User
  const canceledBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }

  const spaceAvailabilityStatus = (
    await findSpaceById(scanRequest.space._id.toString(), 'availabilityStatus')
  )?.availabilityStatus

  const result = await scanRequestRepo.updateScanRequest(
    { _id: id },
    {
      status: ScanRequestStatus.CANCELED,
      reason,
      canceledBy,
      canceledAt: new Date(),
      ...(spaceAvailabilityStatus && {
        spaceAvailabilityWhenCanceled: spaceAvailabilityStatus
      })
    }
  )

  logInfo(location, 'Scan request canceled.', result)
  return result
}

/**
 * getScanAndUpdateList
 * Retrieves and formats scan requests by job ID and status
 * @param jobId - Job ID to filter scan requests
 * @param statuses - Status list for filtering
 * @returns Promise<GetListResponse> - Formatted scan and update lists
 */
const getScanAndUpdateList = async (
  jobId: string,
  statuses: ScanRequestStatusList
): Promise<GetListResponse> => {
  const scanRequests = await findScanRequestsByQuery({
    deletedAt: null,
    status: { $in: Object.values(statuses) },
    jobId
  })

  const scansList = formatScanRequests(
    scanRequests.filter(
      (scanRequest) => scanRequest.status === statuses.forList
    )
  )
  const updatedList = formatScanRequests(
    scanRequests.filter(
      (scanRequest) => scanRequest.status === statuses.forUpdate
    )
  )

  return { scansList, updatedList }
}

/**
 * skipScanRequest
 * Skips a scan request with specified reason and optional comment
 * @param id - The scan request ID to skip
 * @param reason - Optional reason for skipping
 * @param adminUser - Optional admin user performing the skip
 * @param comment - Optional comment for the skip
 * @returns Promise<ScanRequest> - Skipped scan request
 */
export const skipScanRequest = async (
  id: string,
  reason?: string,
  adminUser?: User,
  comment?: string
) => {
  logInfo(location, 'Skipping scan request.', { scanRequestId: id, reason })
  if (!reason) throw new BadRequestError('Reason is required.')
  const scanRequest = await scanRequestRepo.findScanRequestById(id)
  if (!scanRequest) throw new NotFoundError('Scan request not found.')

  const spaceAvailabilityStatus = (
    await findSpaceById(scanRequest.space._id.toString(), 'availabilityStatus')
  )?.availabilityStatus

  switch (scanRequest.status) {
    case ScanRequestStatus.CANCELED: {
      logWarn(
        location,
        'Tried to SKIP a CANCELED scan request.',
        JSON.stringify(scanRequest)
      )
      return scanRequest
    }
    case ScanRequestStatus.COMPLETED: {
      logWarn(
        location,
        'Tried to SKIP a COMPLETED scan request.',
        JSON.stringify(scanRequest)
      )
      return scanRequest
    }
    case ScanRequestStatus.SKIPPED: {
      logInfo(location, 'Skipping a SKIPPED scan request.', {
        scanRequestId: id,
        reason
      })
      if (reason === scanRequest.reason) {
        logInfo(location, 'Reason is same as previous reason')
        return scanRequest
      }
      return await scanRequestRepo.updateScanRequest(
        { _id: id },
        {
          reason,
          comment,
          ...(spaceAvailabilityStatus && {
            spaceAvailabilityWhenSkipped: spaceAvailabilityStatus
          })
        }
      )
    }

    default: {
      let skippedBy: Pick<User, '_id' | 'name' | 'email'>
      if (adminUser) {
        logDebug(location, 'Skipped by admin user.', adminUser)
        skippedBy = {
          _id: adminUser._id.toString(),
          name: adminUser.name,
          email: adminUser.email
        }
      } else {
        const authUser = getAuthUser() as User
        skippedBy = {
          _id: authUser._id.toString(),
          name: authUser.name,
          email: authUser.email
        }
      }

      const result = await scanRequestRepo.updateScanRequest(
        { _id: id },
        {
          status: ScanRequestStatus.SKIPPED,
          skippedAt: new Date(),
          skippedBy,
          reason,
          comment,
          ...(spaceAvailabilityStatus && {
            spaceAvailabilityWhenSkipped: spaceAvailabilityStatus
          })
        }
      )
      if (!result) {
        throw new BadRequestError('Error in skipping scan request.')
      }
      if (reason === 'Ran out of time') {
        await scanRequestRepo.createScanRequests([
          {
            ...result,
            status: ScanRequestStatus.REQUESTED,
            scheduledFor: undefined,
            requestedAt: new Date(),
            skippedAt: undefined,
            skippedBy: undefined,
            reason: undefined,
            createdAt: undefined,
            updatedAt: undefined,
            _id: undefined,
            comment: undefined,
            spaceAvailabilityWhenSkipped: undefined
          }
        ])
      }
      return result
    }
  }
}

/**
 * scheduleScanRequest
 * Schedules a scan request for a specific date with scanner details
 * @param id - The scan request ID to schedule
 * @param scheduledFor - Optional date to schedule the scan
 * @param scannerArrivalWindow - Optional scanner arrival time window
 * @param scannerEmail - Optional scanner email address
 * @returns Promise<ScanRequest> - Scheduled scan request
 */
export const scheduleScanRequest = async (
  id: string,
  scheduledFor?: Date,
  scannerArrivalWindow?: { from: string; to: string },
  scannerEmail?: string
): Promise<ScanRequest> => {
  logInfo(location, 'Scheduling scan request.', id)
  if (!scheduledFor) throw new BadRequestError('Scheduled for is required.')
  if (!isValid(new Date(scheduledFor)))
    throw new BadRequestError('Scheduled for is not valid.')

  const currentScheduledFor = new Date(scheduledFor)
  currentScheduledFor.setHours(0, 0, 0, 0)
  const now = new Date()
  now.setHours(0, 0, 0, 0)
  if (currentScheduledFor < now) {
    throw new BadRequestError('Scheduled for date cannot be in the past.')
  }

  const scanRequest = await scanRequestRepo.findScanRequestById(id)
  const previousScheduledForDate = scanRequest.scheduledFor
  const previousScannerArrivalWindow = scanRequest.scannerArrivalWindow

  if (!scanRequest) throw new NotFoundError('Scan request not found.')

  if (
    scanRequest.status !== ScanRequestStatus.REQUESTED &&
    scanRequest.status !== ScanRequestStatus.SCHEDULED
  ) {
    throw new BadRequestError('Scan request cannot be scheduled.')
  }

  const authUser = getAuthUser()

  const scheduledBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }

  const spaceAvailabilityStatus = (
    await findSpaceById(scanRequest.space._id.toString(), 'availabilityStatus')
  )?.availabilityStatus

  try {
    const updateResult = await scanRequestRepo.updateScanRequest(
      { _id: id },
      {
        status: ScanRequestStatus.SCHEDULED,
        scheduledFor: currentScheduledFor,
        scheduledBy,
        scheduledAt: new Date(),
        scannerArrivalWindow,
        ...(spaceAvailabilityStatus && {
          spaceAvailabilityWhenScheduled: spaceAvailabilityStatus
        }),
        scannerEmail,
        isRescheduled: !!scanRequest.scheduledFor,
        previousScheduledForDate,
        previousScannerArrivalWindow
      }
    )
    logInfo(location, 'Scan request scheduled.', updateResult)

    return updateResult
  } catch (error) {
    throw new BadRequestError(
      `Error updating scan request with id: ${id}: ${error.message}`
    )
  }
}

/**
 * deleteScanRequestById
 * Deletes a scan request by ID
 * @param _id - The scan request ID to delete
 * @returns Promise<ScanRequest> - Deleted scan request
 */
export const deleteScanRequestById = async (
  _id: string
): Promise<ScanRequest> => {
  const authUser = getAuthUser() as User
  const deletedBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }

  return await scanRequestRepo.deleteScanRequest({ _id, deletedBy })
}

/**
 * findScanRequestById
 * Finds a scan request by its ID
 * @param _id - The scan request ID to find
 * @returns Promise<ScanRequest> - Found scan request
 */
export const findScanRequestById = async (
  _id: string
): Promise<ScanRequest> => {
  return await scanRequestRepo.findScanRequestById(_id)
}

/**
 * findScanRequestsCountByCommunities
 * Finds scan request counts grouped by communities
 * @param query - Query parameters for filtering by communities
 * @returns Promise<ScanRequest[]> - Array of scan request counts by community
 */
export const findScanRequestsCountByCommunities = async (
  query: FindScanRequestsCountByCommunitiesDTO
): Promise<ScanRequest[]> => {
  return await scanRequestRepo.findScanRequestsCountByCommunities(query)
}

/**
 * findSpace
 * Finds a space by ID with specified fields
 * @param spaceId - The space ID to find
 * @param fields - Fields to include in the result
 * @returns Promise<Space> - Found space
 */
const findSpace = async (spaceId: string, fields: string) => {
  const result = (
    await findSpacesByQuery({ _id: spaceId }, fields, {
      skipAclScope: true
    })
  )[0]
  if (!result) throw new NotFoundError('Space not found')
  return result
}

/**
 * getRecipients
 * Gets users who should receive notifications based on community and role
 * @param communityId - The community ID to get recipients for
 * @param aliases - Array of role aliases to filter users
 * @param userTag - User tag for additional filtering
 * @returns Promise<User[]> - Array of recipient users
 */
export const getRecipients = async (
  communityId: string,
  aliases: RoleAliases[],
  userTag: UserTag
) => {
  const rolesQuery = { alias: { $in: aliases } }
  logDebug(location, 'Roles query:', rolesQuery)
  const roles = await findRolesByQuery(rolesQuery, '_id', {
    skipAclScope: true
  })
  logDebug(location, 'Roles found:', roles)

  const userQuery = {
    'communities._id': { $in: [communityId] },
    status: 'active',
    roleId: {
      $in: [...roles.map((role) => role._id.toString())]
    }
  }
  logDebug(location, 'User query:', userQuery)

  if (userTag) {
    Object.entries(userTag).map(([key, value]) => {
      userQuery[key] = value
    })
    logDebug(location, 'User query with tags:', userQuery)
  }

  return await findUsers(userQuery, { skipAclScope: true })
}
