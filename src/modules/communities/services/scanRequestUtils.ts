import BadRequestError from '@core/errors/badRequestError'
import NotFoundError from '@core/errors/notFoundError'
import { logDebug, logError, logInfo } from '@core/log'
import { buildCalendarLinks, KLAVIYO_EVENTS, sendMail } from '@core/mail'
import { User } from '@modules/users/types/user'
import { format, isDate } from 'date-fns'
import * as scanRequestRepo from '../repositories/scanRequest'
import { Community } from '../types/community'
import {
  GroupScanRequest,
  GroupScanRequestParams,
  ScanRequest,
  ScanRequestStatus
} from '../types/scanRequest'

const location = 'src/modules/communities/services/scanRequestUtils.ts'

export type FormattedScanRequest = {
  order: number
  unit: string
  floorPlan: string
  reason?: string
}

export type GetListResponse = {
  scansList: FormattedScanRequest[]
  updatedList: FormattedScanRequest[]
}

export type ScanRequestStatusList = {
  forList: ScanRequestStatus
  forUpdate: ScanRequestStatus
}

export type EmailParams = {
  communityName: string
  scansList: FormattedScanRequest[]
  scansListLength: number
  updatedList: FormattedScanRequest[]
  updatedListLength: number
  scanRequestsDashboardURL: string
  dateString: string
  userName: string
  appleCalendarLink: string
  googleCalendarLink: string
  outlookCalendarLink: string
}

export type RescheduledEmailParams = EmailParams & {
  originalScheduledForDate: string
  newScheduledForDate: string
  scannerArrivalWindow: string
}

export type ScannerEmailParams = {
  userName: string
  scannerEmail: string
  scheduledFor: string
  scannerArrivalTime: {
    from: string
    to: string
  }
  community: {
    name: string
    address: string
  }
  scansList: Array<{
    order: number
    unit: string
    floorPlan: string
    building: {
      name: string
      address: string
    }
  }>
}

export const updateAllScanRequestByCommunityId = async (
  communityId: string,
  updateData: Partial<ScanRequest>
): Promise<number> => {
  return scanRequestRepo.updateAllScanRequestByCommunityId(
    communityId,
    updateData
  )
}

// Validation helpers
export const validateScheduledRequests = (
  scheduledRequests: ScanRequest[]
): void => {
  if (!scheduledRequests || scheduledRequests.length === 0) {
    throw new NotFoundError('Scan requests not found.')
  }

  // Validate skipped requests
  const skippedRequests = scheduledRequests.filter(
    (sr) => sr.status === ScanRequestStatus.SKIPPED
  )
  if (skippedRequests.length) {
    throw new BadRequestError(
      'Skipped scan requests with status cannot be rescheduled.'
    )
  }

  const firstRequest = scheduledRequests[0]
  if (!firstRequest.community?._id) {
    throw new BadRequestError(
      'Invalid scan request: missing community information.'
    )
  }

  if (!firstRequest.scheduledFor) {
    throw new BadRequestError('Invalid scan request: missing scheduled date.')
  }

  if (
    !firstRequest.scannerArrivalWindow?.from ||
    !firstRequest.scannerArrivalWindow?.to
  ) {
    throw new BadRequestError(
      'Invalid scan request: missing scanner arrival window.'
    )
  }
}

export const validateUsers = (users: User[]): void => {
  if (!users || users.length === 0) {
    throw new NotFoundError(
      '[sendScheduledConfirmation] Users eligible to receive the email not found.'
    )
  }
}

// Email parameter builders
export const buildBaseEmailParams = (
  scheduledRequest: ScanRequest,
  scansList: FormattedScanRequest[],
  updatedList: FormattedScanRequest[],
  userName: string,
  calendarLinks: { apple: string; google: string; outlook: string },
  formattedDateString: string
): EmailParams => {
  return {
    communityName: scheduledRequest.community.name,
    scansList,
    scansListLength: scansList.length,
    updatedList,
    updatedListLength: updatedList.length,
    scanRequestsDashboardURL: `${process.env.AGENT_DASHBOARD_WEB_URL}/scan-requests/upcoming-shoots`,
    dateString: formattedDateString,
    userName,
    appleCalendarLink: calendarLinks.apple,
    googleCalendarLink: calendarLinks.google,
    outlookCalendarLink: calendarLinks.outlook
  }
}

export const buildScannerEmailParams = (
  scanner: User,
  scanRequests: ScanRequest[],
  community: Community,
  buildingAddress: string,
  communityAddress: string
): ScannerEmailParams => {
  return {
    userName: scanner.name,
    scannerEmail: scanRequests[0].scannerEmail,
    scheduledFor: new Intl.DateTimeFormat('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    }).format(scanRequests[0].scheduledFor),
    scannerArrivalTime: {
      from: scanRequests[0].scannerArrivalWindow.from,
      to: scanRequests[0].scannerArrivalWindow.to
    },
    community: {
      name: community.name,
      address: communityAddress
    },
    scansList: scanRequests.map((scanRequest, i) => ({
      order: i + 1,
      unit: scanRequest.space?.unit ?? '',
      floorPlan: scanRequest.space?.floorPlan?.name || '',
      building: {
        name: scanRequest.space?.building?.name || '',
        address: buildingAddress || ''
      }
    }))
  }
}

// Email sending helpers
export const sendUserConfirmationEmail = async (
  user: User,
  klaviyoTemplate: string,
  params: EmailParams
): Promise<void> => {
  try {
    await sendMail(user.email, klaviyoTemplate, params)
    logDebug(location, `Successfully sent confirmation email to ${user.email}`)
  } catch (error) {
    logError(
      location,
      `Error sending confirmation email to ${user.email.substring(
        user.email.indexOf('@') + 1,
        user.email.length - 1
      )}...`,
      { error, userId: user._id, template: klaviyoTemplate }
    )
  }
}

export const sendRescheduledEmail = async (
  user: User,
  params: RescheduledEmailParams
): Promise<void> => {
  try {
    await sendMail(user.email, KLAVIYO_EVENTS.SCAN_REQUESTS_RESCHEDULED, params)
    logDebug(location, `Successfully sent rescheduled email to ${user.email}`)
  } catch (error) {
    logError(
      location,
      `Error sending rescheduled email to ${user.email.substring(
        user.email.indexOf('@') + 1,
        user.email.length - 1
      )}...`,
      { error, userId: user._id }
    )
  }
}

export const sendScannerConfirmationEmail = async (
  scannerEmail: string,
  params: ScannerEmailParams
): Promise<void> => {
  try {
    await sendMail(
      scannerEmail,
      KLAVIYO_EVENTS.SCANNER_SCHEDULED_SCANS_CONFIRMATION,
      params
    )
    logInfo(
      location,
      `Sent scheduled confirmation email to scanner ${scannerEmail}.`
    )
  } catch (error) {
    logError(
      location,
      `Error sending scanner confirmation email to ${scannerEmail}`,
      { error, scannerEmail }
    )
  }
}

/**
 * Filters out scan requests from sourceList that have matching unit-floorPlan combinations in excludeList
 *
 * Performance: O(n + m) where n = sourceList.length, m = excludeList.length
 * This is much better than the previous O(n * m) nested loop approach
 *
 * @param sourceList - The list to filter
 * @param excludeList - The list containing items to exclude
 * @returns Filtered list with duplicates removed
 */
export const filterDuplicateScanRequests = (
  sourceList: FormattedScanRequest[],
  excludeList: FormattedScanRequest[]
): FormattedScanRequest[] => {
  if (excludeList.length === 0) return sourceList

  // Create a unique key for each scan request (handles null/undefined values)
  const createKey = (item: FormattedScanRequest): string =>
    `${item.unit || 'NO_UNIT'}-${item.floorPlan || 'NO_FLOOR_PLAN'}`

  // Create a Set of unit-floorPlan combinations for O(1) lookup
  const excludeKeys = new Set(excludeList.map(createKey))

  return sourceList.filter((item) => !excludeKeys.has(createKey(item)))
}

// Address building helper
export const buildAddresses = (
  scanRequest: ScanRequest
): { building: string; community: string } => {
  let buildingAddress = ''
  let communityAddress = ''

  if (scanRequest.space?.building?.address) {
    const addr = scanRequest.space.building.address
    buildingAddress = `${addr.street}, ${addr.city}, ${addr.state}, ${addr.postalCode}`
    communityAddress = buildingAddress
  }

  if (communityAddress === '') {
    communityAddress = buildingAddress
  }

  return { building: buildingAddress, community: communityAddress }
}

export const isRequestedByAdmin = (scanRequest: ScanRequest) =>
  scanRequest?.requestedBy?.email?.includes('@peek.us')

export const maskRequestedBy = (scanRequest: ScanRequest) => {
  const isAdmin = isRequestedByAdmin(scanRequest)
  return {
    _id: scanRequest.requestedBy._id,
    name: isAdmin ? 'Peek Team' : scanRequest.requestedBy.name,
    email: isAdmin ? '<EMAIL>' : scanRequest.requestedBy.email
  }
}

export const generateJobId = (
  communityId: string,
  scheduledFor: Date
): string => {
  return `${communityId}-${scheduledFor
    .toISOString()
    .split('T')[0]
    .replace(/-/g, '')}`
}

export const groupScanRequests = ({
  groupBy,
  groupByName,
  scanRequests
}: GroupScanRequestParams): GroupScanRequest[] => {
  const valueName = groupByName ? groupByName : 'data'

  const resultGroupByDateMap = scanRequests.reduce<
    Record<string, ScanRequest[]>
  >((acc, scanRequest) => {
    let value = scanRequest[groupBy]

    if (
      !value &&
      scanRequest.scheduledFor &&
      scanRequest.community?._id &&
      groupBy === 'jobId'
    ) {
      const date = format(scanRequest.scheduledFor, 'yyyyMMdd')
      value = `${scanRequest.community._id}-${date}`
    }

    if (value) {
      if (isDate(value)) value = value.toISOString().split('T')[0]
      if (!acc[value]) acc[value] = []
      acc[value].push(scanRequest)
    }
    return acc
  }, {})

  return Object.keys(resultGroupByDateMap).reduce((acc, key) => {
    acc.push({ date: key, [valueName]: resultGroupByDateMap[key] })
    return acc
  }, [])
}

export const formatScanRequest = (
  scanRequest: ScanRequest,
  index: number
): FormattedScanRequest => ({
  order: index + 1,
  unit: scanRequest.space?.unit,
  floorPlan: scanRequest.space?.floorPlan?.name,
  reason: scanRequest.reason
})

export const formatScanRequests = (
  scanRequests: ScanRequest[]
): FormattedScanRequest[] => scanRequests.map(formatScanRequest)

export const getJobId = (scanRequest: ScanRequest): string => {
  let jobId = scanRequest?.jobId
  if (!jobId && scanRequest?.community?._id && scanRequest.scheduledFor) {
    jobId = `${scanRequest.community._id.toString()}-${format(
      scanRequest.scheduledFor,
      'yyyy-MM-dd'
    )}`
  }
  return jobId
}

export const formatScanRequestDateString = (scanRequest: ScanRequest) => {
  if (
    !scanRequest.scheduledFor ||
    !scanRequest.scannerArrivalWindow.from ||
    !scanRequest.scannerArrivalWindow.to
  ) {
    return undefined
  }

  return `${new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric'
  }).format(scanRequest.scheduledFor)} at ${
    scanRequest.community.name
  }. The scanner will arrive between ${
    scanRequest.scannerArrivalWindow.from
  } and ${scanRequest.scannerArrivalWindow.to}`
}

export const formatCanceledScanRequestDateString = (
  scanRequest: ScanRequest
) => {
  if (
    !scanRequest.scheduledFor ||
    !scanRequest.scannerArrivalWindow.from ||
    !scanRequest.scannerArrivalWindow.to
  ) {
    return undefined
  }

  const textTemplate = 'The scanner will arrive between {from} and {to}...'
  return textTemplate
    .replace('{from}', scanRequest.scannerArrivalWindow.from)
    .replace('{to}', scanRequest.scannerArrivalWindow.to)
}

export const getCalendar = (scanRequest: ScanRequest) => {
  if (!scanRequest.scannerArrivalWindow) return undefined

  const year = scanRequest.scheduledFor.getFullYear()
  const month = scanRequest.scheduledFor.getMonth()
  const day = scanRequest.scheduledFor.getDate()

  const extractTimeParts = (timeStr: string) => {
    const [hour, minuteWithMeridiem] = timeStr.split(':') // Ex: ['04', '00 p.m.']
    const minute = minuteWithMeridiem.substring(0, 2) // '00'
    const meridiem = minuteWithMeridiem.toLowerCase().includes('p.m.')
      ? 'PM'
      : 'AM'

    let hour24 = Number.parseInt(hour, 10)

    if (meridiem === 'PM' && hour24 !== 12) {
      hour24 += 12
    } else if (meridiem === 'AM' && hour24 === 12) {
      hour24 = 0
    }

    return { hour24, minute: Number.parseInt(minute, 10) }
  }

  const { hour24: fromHour, minute: fromMinute } = extractTimeParts(
    scanRequest.scannerArrivalWindow.from
  )
  const { hour24: toHour, minute: toMinute } = extractTimeParts(
    scanRequest.scannerArrivalWindow.to
  )

  const startDate = new Date(year, month, day, fromHour, fromMinute)
  const endDate = new Date(year, month, day, toHour, toMinute)

  const description = `This is the scanner's arrival window - the actual photoshoot might go longer depending on the number of spaces being captured.<br>
 Visit Peek's dashboard to a more complete view on the photoshoots scheduled for the day: ${process.env.AGENT_DASHBOARD_WEB_URL}/scan-requests/upcoming-shoots.<br>Please take a look at the Photoshoot Prep Guide if you haven't done so yet: ${process.env.PHOTOSHOOT_PREP_GUIDE_URL}.`

  return buildCalendarLinks({
    title: `Peek Photoshoot at ${scanRequest.community.name}`,
    description: description,
    location: scanRequest.community.name,
    start: startDate,
    end: endDate
  })
}
