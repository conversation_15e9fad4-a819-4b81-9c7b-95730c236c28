import { getEnvVariable } from '@core/util'

export const virtualTourUrl = (token: string) => {
  return `${getEnvVariable(
    'WEB_VIEWER_BASE_URL'
  )}?token=${token}&showListingDetails=true`
}

export const selfGuidedTourUrl = (communityId: string) => {
  return `${getEnvVariable('SGT_WEB_URL')}/${communityId}/self-guided-tour/`
}

export const agentGuidedTourUrl = (communityId: string) => {
  return `${getEnvVariable('SGT_WEB_URL')}/${communityId}/agent-guided-tour/`
}

export const welcomeSiteUrl = (communityId: string) => {
  return `${getEnvVariable('WELCOME_SITE_BASE_URL')}/${communityId}`
}
