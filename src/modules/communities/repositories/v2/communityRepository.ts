import {
  CrudRepositoryMongoDb,
  FindOptions,
  Query as CrudQuery,
  FindOneOptions
} from '@core/repositories/CrudRepository'
import { crudRepositoryMongoDbFactory } from '@core/repositories/crudRepositoryFactory'
import { getAclScopeQueryV2 } from '@core/auth'
import { PipelineStage } from 'mongoose'
import { CommunityModel } from '@modules/communities/models/community'
import { Community } from '@modules/communities/types/community'
import { ObjectId } from '@modules/communities/types/id'

export type CommunityRepository = CrudRepositoryMongoDb<Community>

const baseRepo = crudRepositoryMongoDbFactory(CommunityModel)

export const communityRepositoryMongoDb: CommunityRepository = {
  ...baseRepo,
  async findAll(
    query: CrudQuery,
    options: FindOptions = {}
  ): Promise<Community[]> {
    const queryWithAcl = findAclQueryV2(query)
    options.skipAclScope = true
    return baseRepo.findAll(queryWithAcl, options)
  },
  async findById(id: string, options: FindOneOptions = {}): Promise<Community> {
    const queryWithAcl = findAclQueryV2({ _id: id })
    options.skipAclScope = true
    return baseRepo.findOne(queryWithAcl, options)
  },
  async findOne(
    query: CrudQuery,
    options: FindOneOptions = {}
  ): Promise<Community> {
    const queryWithAcl = findAclQueryV2(query)
    options.skipAclScope = true
    return baseRepo.findOne(queryWithAcl, options)
  },
  async update(
    id: string,
    entity: Community,
    options: FindOneOptions = {}
  ): Promise<Community> {
    const queryWithAcl = findAclQueryV2({ _id: id })
    options.skipAclScope = true
    return baseRepo.updateQuery(queryWithAcl, entity, options)
  },
  async updateQuery(
    query: CrudQuery,
    entity: Community,
    options: FindOneOptions = {}
  ): Promise<Community> {
    const queryWithAcl = findAclQueryV2(query)
    options.skipAclScope = true
    return baseRepo.updateQuery(queryWithAcl, entity, options)
  },
  async aggregate<T>(pipeline: PipelineStage[]): Promise<T[]> {
    const queryWithAcl = findAclQueryV2({})
    const pipelineWithScope = [{ $match: queryWithAcl }, ...pipeline]
    return baseRepo.aggregate(pipelineWithScope) as unknown as T[]
  }
}

const findAclQueryV2 = (query: CrudQuery) => {
  const acl = getAclScopeQueryV2()
  const defaultCond = {
    isActive: { $ne: false },
    deletedAt: null
  }
  const allowedCommunityIds = (acl.communities || []).map(
    (id) => new ObjectId(id)
  )
  const allowedOrgIds = (acl.organizations || []).map((id) => new ObjectId(id))
  let requestedIds = []
  if (query._id) {
    if (typeof query._id === 'object' && '$in' in query._id) {
      requestedIds = query._id.$in.map((id: string) => new ObjectId(id))
    } else {
      requestedIds = [new ObjectId(query._id as string)]
    }
    delete query._id
  } else if (query.ids) {
    requestedIds = query.ids.map((id: string) => new ObjectId(id))
    delete query.ids
  }
  if (requestedIds.length > 0) {
    const permitted = requestedIds.filter((rid) => {
      return allowedCommunityIds.some(
        (cid) => cid.toString() === rid.toString()
      )
    })

    if (permitted.length === 0) {
      return { ...defaultCond, _id: { $in: [] } }
    }
    const idFilter =
      permitted.length > 1 ? { _id: { $in: permitted } } : { _id: permitted[0] }
    return { ...defaultCond, ...idFilter }
  }
  let aclCond = {}
  if (allowedCommunityIds.length > 0) {
    aclCond = { _id: { $in: allowedCommunityIds } }
  } else if (allowedOrgIds.length > 0) {
    aclCond = { 'organization._id': { $in: allowedOrgIds } }
  }
  return { ...defaultCond, ...aclCond, ...query }
}
