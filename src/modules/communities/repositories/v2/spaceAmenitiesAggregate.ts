import { spaceRepositoryMongoDb } from '@modules/spacex/repositories/spaceRepository'
import { Community } from '@modules/communities/types/community'
import { escapeRegex, ProjectionFields } from '@core/util'
import { FindOptions } from '@core/repositories/CrudRepository'
import { PipelineStage } from 'mongoose'

/**
 * Returns Communities that have at least one amenity whose unit field matches
 * any of the provided amenity names (case-insensitive) AND satisfy the
 * additional `match` criteria.
 */
export async function findCommunitiesByAmenities(
  amenityNames: string[],
  match: Record<string, any>,
  options?: FindOptions
): Promise<Community[]> {
  const aggregate: PipelineStage[] = [
    {
      $match: {
        type: 'amenity',
        $or: amenityNames.map((unit) => ({
          unit: { $regex: escapeRegex(unit), $options: 'i' }
        }))
      }
    },
    { $group: { _id: '$community' } },
    {
      $lookup: {
        from: 'communities',
        localField: '_id._id',
        foreignField: '_id',
        as: 'community'
      }
    },
    { $unwind: '$community' },
    { $replaceRoot: { newRoot: '$community' } },
    ...(Object.keys(match).length ? [{ $match: match }] : []),
    {
      $project: (options?.projection ?? {}) as ProjectionFields
    }
  ]

  if (options?.skip) aggregate.push({ $skip: options.skip })
  if (options?.limit) aggregate.push({ $limit: options.limit })

  const result = (await spaceRepositoryMongoDb.aggregate(
    aggregate
  )) as unknown as Community[]

  return result
}
