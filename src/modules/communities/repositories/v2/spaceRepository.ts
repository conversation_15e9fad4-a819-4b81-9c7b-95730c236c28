import { getAclScopeQueryV2 } from '@core/auth'
import {
  FindOptions,
  Query as CrudQuery,
  FindOneOptions
} from '@core/repositories/CrudRepository'
import { SpaceModel } from '@modules/communities/models/space'
import { Space } from '@modules/communities/types/space'
import { PipelineStage } from 'mongoose'
import { crudRepositoryMongoDbFactory } from '@core/repositories/crudRepositoryFactory'

const baseRepo = crudRepositoryMongoDbFactory(SpaceModel)

export const spaceRepositoryMongoDb = {
  ...baseRepo,
  async findAll(query: CrudQuery, options: FindOptions = {}): Promise<Space[]> {
    const queryWithAcl = findAclQueryV2(query)

    options.skipAclScope = true
    return baseRepo.findAll({ ...queryWithAcl }, options)
  },
  async findById(id: string, options: FindOneOptions = {}): Promise<Space> {
    const queryWithAcl = findAclQueryV2({ _id: id })

    options.skipAclScope = true
    return baseRepo.findOne(queryWithAcl, options)
  },
  async findOne(
    query: CrudQuery,
    options: FindOneOptions = {}
  ): Promise<Space> {
    const queryWithAcl = findAclQueryV2(query)

    options.skipAclScope = true
    return baseRepo.findOne(queryWithAcl, options)
  },
  async update(
    id: string,
    entity: Space,
    options: FindOneOptions = {}
  ): Promise<Space> {
    const queryWithAcl = findAclQueryV2({ _id: id })

    options.skipAclScope = true
    return baseRepo.updateQuery(queryWithAcl, entity, options)
  },
  async updateQuery(
    query: CrudQuery,
    entity: Space,
    options: FindOneOptions = {}
  ): Promise<Space> {
    const queryWithAcl = findAclQueryV2(query)

    options.skipAclScope = true
    return baseRepo.updateQuery(queryWithAcl, entity, options)
  },
  async aggregate<T>(pipeline: PipelineStage[]): Promise<T[]> {
    const queryWithAcl = findAclQueryV2({})
    const pipelineWithScope = [{ $match: queryWithAcl }, ...pipeline]
    return baseRepo.aggregate(pipelineWithScope) as unknown as T[]
  }
}

const findAclQueryV2 = (query: CrudQuery) => {
  const acl = getAclScopeQueryV2()
  const queryWithAcl = {
    ...query,
    'community.isActive': { $ne: false },
    deletedAt: null
  }
  if (acl?.communities?.length > 0) {
    queryWithAcl['community._id'] = { $in: acl.communities }
  } else if (acl?.organizations?.length > 0) {
    queryWithAcl['community.organization._id'] = { $in: acl.organizations }
  }
  return queryWithAcl
}
