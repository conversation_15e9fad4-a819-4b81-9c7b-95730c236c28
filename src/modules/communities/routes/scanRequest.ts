import { PeekRequest } from '@api/interfaces'
import acl from '@api/middlewares/acl'
import authenticate from '@api/middlewares/auth'
import communityScope from '@api/middlewares/communityScope'
import validateBody from '@api/middlewares/validateBody'
import validateParams from '@api/middlewares/validateParams'
import validateQuery from '@api/middlewares/validateQuery'
import { getAuthUser } from '@core/auth'
import UnauthorizedError from '@core/errors/unauthorizedError'
import { logError } from '@core/log'
import { RoleAliases } from '@modules/users/types/role'
import { Response, Router } from 'express'
import {
  AppleCalendarRequestSchema,
  CreateScanRequestsSchema,
  FindScanRequestsCountByCommunitiesSchema,
  FindScanRequestsSchema,
  ReqParamMongoIdSchema,
  SendScheduledScansConfirmationBody,
  UpdateScanRequestSchema,
  UpdateScanRequestsSchema
} from '../schemas/scanRequest'
import {
  createScanRequests,
  deleteScanRequestById,
  findScanRequestById,
  findScanRequests,
  findScanRequestsCountByCommunities,
  sendScheduledConfirmation,
  updateScanRequests
} from '../services/scanRequest'
import {
  CreateScanRequestsDTO,
  FindScanRequestsCountByCommunitiesDTO,
  FindScanRequestsDTO,
  ScanRequest,
  UpdateScanRequestDTO
} from '../types/scanRequest'
import { groupScanRequests } from '../services/scanRequestUtils'

const router = Router()

router.get(
  '/apple-calendar',
  validateQuery(AppleCalendarRequestSchema),
  async (req, res) => {
    const { title, description, location, start, end } = req.query

    if (!title || !start || !end) {
      return res.status(400).send('Missing required parameters')
    }

    const icsContent = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Test Calendar//EN',
      'CALSCALE:GREGORIAN',
      'METHOD:PUBLISH',
      'BEGIN:VEVENT',
      'UID:test-1',
      `DTSTART:${start}`,
      `DTEND:${end}`,
      `SUMMARY:${title}`,
      `DESCRIPTION: ${description || ''}`,
      `LOCATION:${location || ''}`,
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\n')

    res.setHeader('Content-Type', 'text/calendar; charset=utf-8')
    res.setHeader('Content-Disposition', 'attachment; filename="event.ics"')
    res.send(icsContent)
  }
)

router.post(
  '/',
  authenticate(),
  acl([
    'peek:roles:<roleId>::full',
    'peek:communities:<communityId>::read',
    'peek:communities:<communityId>::full',
    'peek:organizations:<organizationId>::read',
    'peek:organizations:<organizationId>::full'
  ]),
  validateBody(CreateScanRequestsSchema),
  async (req: PeekRequest, res: Response) => {
    const createScanRequest: CreateScanRequestsDTO = req.body

    const authUser = getAuthUser()

    const isAdmin = [
      authUser.roleAlias,
      authUser.proxyUser?.roleAlias
    ].includes(RoleAliases.ADMIN)

    const isScanner = [
      authUser.roleAlias,
      authUser.proxyUser?.roleAlias
    ].includes(RoleAliases.SCANNER)

    if (!isAdmin && createScanRequest.status) {
      throw new UnauthorizedError('You can not set the status')
    }

    if (!(isAdmin || isScanner) && createScanRequest.scheduledFor) {
      throw new UnauthorizedError('You can not schedule a date')
    }

    const result = await createScanRequests(createScanRequest)
    res.status(200).json({ data: result })
  }
)

router.get(
  '/',
  authenticate(),
  communityScope('community._id'),
  validateQuery(FindScanRequestsSchema),
  async (req: PeekRequest, res: Response) => {
    const findScanRequestsParams: FindScanRequestsDTO = req.query
    const { groupBy, groupByName, ...params } = findScanRequestsParams
    const result = await findScanRequests(params)

    if (groupBy) {
      result.data = groupScanRequests({
        groupBy,
        groupByName,
        scanRequests: result.data as ScanRequest[]
      })
    }

    res.status(200).json(result)
  }
)

router.get(
  '/:id',
  authenticate(),
  communityScope('community._id'),
  validateParams(ReqParamMongoIdSchema),
  async (req: PeekRequest, res: Response) => {
    const { id } = req.params
    const result = await findScanRequestById(id)
    res.status(200).json({ data: result })
  }
)

router.get(
  '/communities/count',
  authenticate(),
  acl(['peek:roles:<roleId>::full']),
  validateQuery(FindScanRequestsCountByCommunitiesSchema),
  async (req: PeekRequest, res: Response) => {
    const findScanRequestsParams =
      req.query as FindScanRequestsCountByCommunitiesDTO
    const result = await findScanRequestsCountByCommunities(
      findScanRequestsParams
    )
    res.status(200).json({ data: result })
  }
)

router.put(
  '/:id',
  authenticate(),
  communityScope('community._id'),
  validateParams(ReqParamMongoIdSchema),
  validateBody(UpdateScanRequestSchema),
  async (req: PeekRequest, res: Response) => {
    const { id } = req.params
    const updateScanRequest = req.body as UpdateScanRequestDTO
    const result = await updateScanRequests([{ ...updateScanRequest, _id: id }])
    res.status(200).json({ data: result })
  }
)

router.put(
  '/',
  authenticate(),
  communityScope('community._id'),
  validateBody(UpdateScanRequestsSchema),
  async (req: PeekRequest, res: Response) => {
    const updateScanRequestsDTO = req.body
      .scanRequests as UpdateScanRequestDTO[]
    const result = await updateScanRequests(updateScanRequestsDTO)
    res.status(200).json({ data: result })
  }
)

router.delete(
  '/:id',
  authenticate(),
  communityScope('community._id'),
  validateParams(ReqParamMongoIdSchema),
  async (req: PeekRequest, res: Response) => {
    const { id } = req.params
    const result = await deleteScanRequestById(id)
    res.status(200).json({ data: result })
  }
)

router.post(
  '/scheduled-confirmation',
  authenticate(),
  validateBody(SendScheduledScansConfirmationBody),
  async (req: PeekRequest, res: Response) => {
    try {
      const { ids } = req.body

      if (!ids?.length) {
        throw new Error('No ids found.')
      }

      await sendScheduledConfirmation(ids)
      return res.status(200).json({ data: {} })
    } catch (error) {
      logError(
        `Error sending scheduled confirmation for requests: ${(
          req.body.ids as string[]
        ).join(', ')}`,
        error
      )
      return res
        .status(500)
        .json({ error: 'Error sending scheduled confirmation' })
    }
  }
)

const scanRequest = Router()

scanRequest.use('/scan-requests', router)

export default scanRequest
