import { IdempotentRequest } from '@core/types'
import { model, Schema } from 'mongoose'

const IdempotentRequestsSchema = new Schema<IdempotentRequest>(
  {
    idempotencyKey: {
      type: String,
      required: true
    },
    requestMethod: {
      type: String,
      required: true
    },
    requestUrl: {
      type: String,
      required: true
    },
    requestBody: {
      type: String,
      required: true
    },
    responseBody: {
      type: String,
      required: false
    },
    responseStatus: {
      type: Number,
      required: false
    },
    responseHeaders: {
      type: String,
      required: false
    },
    expiresAt: {
      type: Date,
      required: true,
      expires: 0
    }
  },
  {
    timestamps: true
  }
)

IdempotentRequestsSchema.index({ idempotencyKey: 1 }, { unique: true })

export const IdempotentRequestsModel = model(
  'idempotentRequests',
  IdempotentRequestsSchema
)
