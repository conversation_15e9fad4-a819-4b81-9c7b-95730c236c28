import fs from 'fs'
import csv from 'csv-parser'
import axios from 'axios'

const API_TOKEN = process.argv[2]
const INPUT_CSV = process.argv[3]
const separator = process.argv[4]
const API_URL = 'https://api-v3.peek.us/spaces'

let validated = false

const REQUIRED_FIELDS = [
  'Community ID',
  'Community Name',
  'Unit name',
  'Floor Plan Name',
  'Bedrooms',
  'Bathrooms',
  'Street',
  'Street 2',
  'State',
  'City',
  'Postal Code'
]

fs.createReadStream(INPUT_CSV)
  .pipe(csv({ separator }))
  .on('headers', (headers) => {
    const missing = REQUIRED_FIELDS.filter((field) => !headers.includes(field))
    if (missing.length > 0) {
      console.error(`❌ Missing required fields in CSV: ${missing.join(', ')}`)
      process.exit(1)
    }
    validated = true
    console.log('✅ CSV header validation passed.')
  })
  .on('data', async (row) => {
    if (!validated) return
    const payload = {
      type: 'unit',
      nodes: [],
      building: {
        address: {
          street: row['Street'],
          street2: row['Street 2'],
          city: row['City'],
          state: row['State'],
          postalCode: row['Postal Code'],
          country: 'USA'
        }
      },
      community: {
        _id: row['Community ID'],
        name: row['Community Name']
      },
      unit: row['Unit name'],
      unitSize: row['Unit Size'],
      bedrooms: parseInt(row['Bedrooms']),
      bathrooms: parseFloat(row['Bathrooms']),
      isVisible: true,
      isComplete: false,
      floorPlan: {
        name: row['Floor Plan Name']
      }
    }

    try {
      const res = await axios.post(API_URL, payload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${API_TOKEN}` // remove if not needed
        }
      })
      console.log(`✅ Created unit ${payload.unit}:`, res.data)
    } catch (err) {
      console.error(
        `❌ Failed to create unit ${payload.unit}:`,
        err.response?.data || err.message
      )
    }
  })
  .on('end', () => {
    console.log('🏁 CSV processing complete.')
  })
