/* eslint-disable */
import { connectDb } from '@core/db'
import { loadEnv } from '@core/loadEnv'
import { hotspotToPosition } from '@core/util'
import { SpaceModel } from '@modules/communities/models/space'

loadEnv()

const BATCH_SIZE = 100 // Process spaces in batches
const CONCURRENT_BATCHES = 5 // Number of concurrent batch operations

const run = async () => {
  await connectDb()

  // Only fetch necessary fields to reduce memory usage
  const spaces = await SpaceModel.find({
    deletedAt: null,
    'nodes.0': { $exists: true },
    'nodes.nodeLinks.scale': { $exists: false }
  })
    .setOptions({ skipAclScope: true })
    .select('_id nodes')
    // .limit(10)
    .lean()

  console.log(`Found ${spaces.length} spaces to process`)

  // Process spaces in batches
  const batches = []
  for (let i = 0; i < spaces.length; i += BATCH_SIZE) {
    batches.push(spaces.slice(i, i + BATCH_SIZE))
  }

  let processedCount = 0
  const startTime = Date.now()

  // Process batches with controlled concurrency
  for (let i = 0; i < batches.length; i += CONCURRENT_BATCHES) {
    const currentBatches = batches.slice(i, i + CONCURRENT_BATCHES)

    await Promise.all(
      currentBatches.map(async (batch, batchIndex) => {
        const batchNumber = i + batchIndex + 1
        console.log(`Processing batch ${batchNumber}/${batches.length}`)

        const bulkOps = batch
          .map((space) => {
            if (!Array.isArray(space.nodes)) {
              return null
            }

            const newNodes = space.nodes.map((node) => {
              const newNodeLinks = !Array.isArray(node.nodeLinks)
                ? []
                : node.nodeLinks.map((link) => ({
                  ...link,
                  position: link.rotation
                    ? hotspotToPosition(
                      link.rotation.pitch,
                      link.rotation.yaw
                    )
                    : null,
                  scale: 18
                }))
              return {
                ...node,
                nodeLinks: newNodeLinks
              }
            })

            return {
              updateOne: {
                filter: { _id: space._id },
                update: { $set: { nodes: newNodes } }
              }
            }
          })
          .filter(Boolean)

        await SpaceModel.bulkWrite(bulkOps, { ordered: false })
        processedCount += batch.length
        console.log(
          `Completed batch ${batchNumber}: ${processedCount}/${spaces.length} spaces processed`
        )
      })
    )
  }

  const endTime = Date.now()
  const duration = (endTime - startTime) / 1000
  console.log(`\n✅ Script completed in ${duration.toFixed(2)} seconds`)
  console.log(
    `Processed ${processedCount} spaces at ${(
      processedCount / duration
    ).toFixed(2)} spaces/second`
  )
}

run().catch((err) => {
  console.error('Script failed:', err)
  process.exit(1)
})
