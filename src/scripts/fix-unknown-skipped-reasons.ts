import mongoose from 'mongoose'
import { loadEnv } from '@core/loadEnv'
import { getEnvVariable } from '@core/util'
import { connectDb } from '@core/db'
import { ScanRequestModel } from '@modules/communities/models/scanRequest'
import { ScanRequestStatus } from '@modules/communities/types/scanRequest'
import { ObjectId } from 'mongodb'

loadEnv()

const run = async () => {
  try {
    await connectDb()
    console.log('Connected to MongoDB')

    const worklogJanitorId = getEnvVariable('WORKLOG_JANITOR_ID')
    if (!worklogJanitorId) {
      console.log('WORKLOG_JANITOR_ID not defined; skipping update.')
      return
    }

    const result = await ScanRequestModel.updateMany(
      {
        status: ScanRequestStatus.SKIPPED,
        reason: { $in: [null, '', 'Unknown'] },
        'skippedBy._id': new ObjectId(worklogJanitorId)
      },
      {
        $set: {
          reason: 'Peek Skipped',
          updatedBy: 'PK-1061'
        }
      }
    )

    console.log(
      `Updated ${result.modifiedCount} scan request(s) with skippedReason='Peek Skipped'`
    )
  } catch (error) {
    console.error('Error fixing skipped reasons:', error)
  } finally {
    await mongoose.connection.close()
    console.log('Disconnected from MongoDB')
  }
}

run()
